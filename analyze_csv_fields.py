import pandas as pd
import json

def analyze_csv_detailed_fields():
    """详细分析CSV中的所有字段信息"""
    
    # 加载CSV数据
    csv_data = pd.read_csv('积水点_带地址.csv', encoding='utf-8')
    weiyangqu_csv = csv_data[csv_data['district'] == '未央区'].copy()
    
    print("=== CSV字段分析 ===")
    print(f"CSV列名: {csv_data.columns.tolist()}")
    
    # 分析前3条未央区数据的详细信息
    for idx, row in weiyangqu_csv.head(3).iterrows():
        print(f"\n=== CSV记录 ID {row['id']} 详细信息 ===")
        print(f"经纬度: ({row['lon']}, {row['lat']})")
        print(f"formatted_address: {row['formatted_address']}")
        print(f"province: {row['province']}")
        print(f"city: {row['city']}")
        print(f"district: {row['district']}")
        
        # 解析api_response中的详细信息
        try:
            api_response = json.loads(row['api_response'])
            regeocode = api_response['regeocode']
            
            print(f"\n--- API响应详细信息 ---")
            print(f"formatted_address: {regeocode['formatted_address']}")
            
            # 地址组件
            if 'addressComponent' in regeocode:
                addr_comp = regeocode['addressComponent']
                print(f"详细地址组件:")
                print(f"  省份: {addr_comp.get('province', '')}")
                print(f"  城市: {addr_comp.get('city', '')}")
                print(f"  区县: {addr_comp.get('district', '')}")
                print(f"  街道: {addr_comp.get('township', '')}")
                
                if 'streetNumber' in addr_comp:
                    street_num = addr_comp['streetNumber']
                    print(f"  街道号码: {street_num.get('street', '')} {street_num.get('number', '')}")
            
            # 道路信息
            if 'roads' in regeocode:
                print(f"道路信息 ({len(regeocode['roads'])}条):")
                for i, road in enumerate(regeocode['roads'][:5]):  # 显示前5条
                    print(f"  {i+1}. {road['name']} (距离: {road['distance']}米, 方向: {road['direction']})")
            
            # 路口信息
            if 'roadinters' in regeocode:
                print(f"路口信息 ({len(regeocode['roadinters'])}个):")
                for i, inter in enumerate(regeocode['roadinters'][:3]):  # 显示前3个
                    print(f"  {i+1}. {inter['first_name']} 与 {inter['second_name']} (距离: {inter['distance']}米)")
            
            # POI信息
            if 'pois' in regeocode:
                print(f"POI信息 ({len(regeocode['pois'])}个):")
                for i, poi in enumerate(regeocode['pois'][:3]):  # 显示前3个
                    print(f"  {i+1}. {poi['name']} ({poi['type']}, 距离: {poi['distance']}米)")
            
            # AOI信息
            if 'aois' in regeocode:
                print(f"AOI信息 ({len(regeocode['aois'])}个):")
                for i, aoi in enumerate(regeocode['aois'][:3]):  # 显示前3个
                    print(f"  {i+1}. {aoi['name']} ({aoi['type']}, 距离: {aoi['distance']}米)")
                    
        except Exception as e:
            print(f"解析API响应时出错: {e}")
    
    # 分析Excel数据
    print(f"\n\n=== Excel风险点数据样本 ===")
    excel_data = pd.read_excel('ocr表格1h201.9mm.xlsx')
    weiyangqu_excel = excel_data[excel_data['行政区'] == '未央区'].copy()
    
    print(f"Excel列名: {excel_data.columns.tolist()}")
    print(f"\n未央区风险点样本:")
    for idx, row in weiyangqu_excel.head(10).iterrows():
        print(f"{row['编号']}: {row['风险点']} ({row['风险等级']})")

if __name__ == "__main__":
    analyze_csv_detailed_fields()
