#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WGS84经纬度反查中文地址脚本
使用高德地图API进行逆地理编码
"""

import pandas as pd
import requests
import time
import json
import math
from typing import Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from functools import wraps

class Config:
    """配置类"""
    # 并发设置
    MAX_WORKERS = 5  # 并发线程数，建议不超过10，避免被API限流

    # 重试设置
    MAX_RETRIES = 3  # 最大重试次数
    RETRY_DELAY = 0.5  # 重试延迟（秒）
    RETRY_BACKOFF = 2.0  # 重试延迟倍数

    # API设置
    REQUEST_TIMEOUT = 10  # 请求超时时间（秒）

def retry_on_failure(max_retries=3, delay=1.0, backoff=2.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries >= max_retries:
                        print(f"重试{max_retries}次后仍然失败: {e}")
                        return None

                    wait_time = delay * (backoff ** (retries - 1))
                    print(f"第{retries}次重试失败，{wait_time:.1f}秒后重试: {e}")
                    time.sleep(wait_time)
            return None
        return wrapper
    return decorator

class CoordinateConverter:
    """坐标转换工具类：WGS84 -> GCJ-02"""
    
    # 椭球参数
    a = 6378245.0  # 长半轴
    ee = 0.00669342162296594323  # 偏心率平方
    
    @classmethod
    def wgs84_to_gcj02(cls, lng: float, lat: float) -> Tuple[float, float]:
        """
        WGS84坐标转换为GCJ-02坐标
        """
        if cls._out_of_china(lng, lat):
            return lng, lat
        
        dlat = cls._transform_lat(lng - 105.0, lat - 35.0)
        dlng = cls._transform_lng(lng - 105.0, lat - 35.0)
        
        radlat = lat / 180.0 * math.pi
        magic = math.sin(radlat)
        magic = 1 - cls.ee * magic * magic
        sqrtmagic = math.sqrt(magic)
        
        dlat = (dlat * 180.0) / ((cls.a * (1 - cls.ee)) / (magic * sqrtmagic) * math.pi)
        dlng = (dlng * 180.0) / (cls.a / sqrtmagic * math.cos(radlat) * math.pi)
        
        mglat = lat + dlat
        mglng = lng + dlng
        
        return mglng, mglat
    
    @classmethod
    def _out_of_china(cls, lng: float, lat: float) -> bool:
        """判断是否在中国境外"""
        return not (72.004 <= lng <= 137.8347 and 0.8293 <= lat <= 55.8271)
    
    @classmethod
    def _transform_lat(cls, lng: float, lat: float) -> float:
        """纬度转换"""
        ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + \
              0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * 
                math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lat * math.pi) + 40.0 * 
                math.sin(lat / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (160.0 * math.sin(lat / 12.0 * math.pi) + 320 * 
                math.sin(lat * math.pi / 30.0)) * 2.0 / 3.0
        return ret
    
    @classmethod
    def _transform_lng(cls, lng: float, lat: float) -> float:
        """经度转换"""
        ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + \
              0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * 
                math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lng * math.pi) + 40.0 * 
                math.sin(lng / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (150.0 * math.sin(lng / 12.0 * math.pi) + 300.0 * 
                math.sin(lng / 30.0 * math.pi)) * 2.0 / 3.0
        return ret


class AmapGeocoder:
    """高德地图逆地理编码类"""
    
    def __init__(self, api_key: str):
        self.api_key = "0b65cd528f00db9f2cb533a1b2fee65f"
        self.base_url = "https://restapi.amap.com/v3/geocode/regeo"
        self.session = requests.Session()
        
    @retry_on_failure(max_retries=Config.MAX_RETRIES, delay=Config.RETRY_DELAY, backoff=Config.RETRY_BACKOFF)
    def reverse_geocode(self, lng: float, lat: float, debug: bool = False) -> Optional[dict]:
        """
        逆地理编码：根据经纬度获取地址
        返回高德地图API的完整原始响应数据
        """
        # 转换坐标系：WGS84 -> GCJ-02
        gcj_lng, gcj_lat = CoordinateConverter.wgs84_to_gcj02(lng, lat)

        if debug:
            print(f"  原始坐标 (WGS84): {lng}, {lat}")
            print(f"  转换坐标 (GCJ02): {gcj_lng}, {gcj_lat}")

        params = {
            'key': self.api_key,
            'location': f"{gcj_lng},{gcj_lat}",
            'radius': 100,
            'extensions': 'all'
        }

        if debug:
            print(f"  请求URL: {self.base_url}")
            print(f"  请求参数: {params}")

        try:
            response = self.session.get(self.base_url, params=params, timeout=Config.REQUEST_TIMEOUT)

            if debug:
                print(f"  响应状态码: {response.status_code}")
                print(f"  响应内容: {response.text[:500]}...")

            response.raise_for_status()

            data = response.json()

            if data.get('status') == '1' and data.get('regeocode'):
                if debug:
                    print(f"  API响应成功，返回完整数据")
                    print(f"  格式化地址: {data['regeocode'].get('formatted_address', '')}")

                    # 显示主要组件信息
                    regeocode = data['regeocode']
                    addressComponent = regeocode.get('addressComponent', {})
                    roads = regeocode.get('roads', [])
                    pois = regeocode.get('pois', [])

                    print(f"  地址组件: {addressComponent}")
                    print(f"  道路数量: {len(roads)}")
                    print(f"  POI数量: {len(pois)}")

                # 返回完整的API响应数据
                return data
            else:
                error_msg = data.get('info', 'Unknown error')
                print(f"API返回错误: status={data.get('status')}, info={error_msg}")
                if debug:
                    print(f"  完整响应: {data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            if debug:
                print(f"  原始响应: {response.text}")
            return None


def process_single_point(args):
    """处理单个点的函数，用于并发执行"""
    geocoder, lng, lat, point_id, index, total = args

    try:
        result = geocoder.reverse_geocode(lng, lat, debug=False)
        if result and result.get('regeocode'):
            addr = result['regeocode'].get('formatted_address', '')[:50]
            print(f"✓ {index + 1:3d}/{total} - ID:{point_id} - {addr}...")
        else:
            print(f"✗ {index + 1:3d}/{total} - ID:{point_id} - 无数据")
        return result
    except Exception as e:
        print(f"✗ {index + 1:3d}/{total} - ID:{point_id} - 错误: {str(e)[:30]}...")
        return None

def main():
    """主函数"""
    # 高德地图API Key - 请替换为您的实际API Key
    API_KEY = "0b65cd528f00db9f2cb533a1b2fee65f"

    print(f"调试信息: 当前API Key = {API_KEY}")
    print(f"调试信息: API Key长度 = {len(API_KEY)}")

    if not API_KEY or API_KEY == "YOUR_AMAP_API_KEY_HERE":
        print("错误: 请先设置您的高德地图API Key")
        print("请访问 https://console.amap.com/ 申请API Key")
        return

    # 并发设置
    MAX_WORKERS = Config.MAX_WORKERS
    print(f"并发线程数: {MAX_WORKERS}")
    print(f"重试设置: 最大{Config.MAX_RETRIES}次，延迟{Config.RETRY_DELAY}秒")
    
    # 读取CSV文件
    input_file = "1h201.9-积水点.csv"
    output_file = "积水点_带地址.csv"
    
    try:
        df = pd.read_csv(input_file)
        print(f"成功读取 {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件错误: {e}")
        return
    
    # 初始化地理编码器
    geocoder = AmapGeocoder(API_KEY)
    
    # 存储API响应数据
    api_responses = []
    
    print("开始逆地理编码...")

    # 先测试第一个点，启用详细调试
    if len(df) > 0:
        first_row = df.iloc[0]
        print(f"\n=== 测试第一个点 (ID: {first_row['id']}) ===")
        test_result = geocoder.reverse_geocode(first_row['lon'], first_row['lat'], debug=True)
        if test_result:
            print(f"测试成功 - 获取到完整API响应数据")
            print(f"格式化地址: {test_result.get('regeocode', {}).get('formatted_address', '')}")
        else:
            print("测试失败 - 未获取到数据")
        print("=" * 50)

        # 询问用户是否继续
        user_input = input("\n测试完成，是否继续处理所有点？(y/n): ").lower().strip()
        if user_input != 'y':
            print("用户取消操作")
            return

    # 准备并发任务
    tasks = []
    for index, row in df.iterrows():
        lng = row['lon']
        lat = row['lat']
        point_id = row['id']
        tasks.append((geocoder, lng, lat, point_id, index, len(df)))

    print(f"\n开始并发处理 {len(tasks)} 个点...")
    start_time = time.time()

    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_index = {executor.submit(process_single_point, task): i for i, task in enumerate(tasks)}

        # 收集结果
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            try:
                result = future.result()
                api_responses.append((index, result))
            except Exception as e:
                print(f"任务 {index + 1} 执行异常: {e}")
                api_responses.append((index, None))

    # 按原始顺序排序结果
    api_responses.sort(key=lambda x: x[0])
    api_responses = [result for _, result in api_responses]

    end_time = time.time()
    print(f"\n并发处理完成，耗时: {end_time - start_time:.2f} 秒")
    print(f"成功处理: {sum(1 for r in api_responses if r is not None)} 个点")
    print(f"失败: {sum(1 for r in api_responses if r is None)} 个点")
    
    # 添加API响应数据到DataFrame
    # 将完整的API响应转换为JSON字符串
    df['api_response'] = [json.dumps(resp, ensure_ascii=False) if resp else "" for resp in api_responses]

    # 同时提取一些常用字段便于查看
    formatted_addresses = []
    provinces = []
    cities = []
    districts = []

    for resp in api_responses:
        if resp and resp.get('regeocode'):
            regeocode = resp['regeocode']
            formatted_addresses.append(regeocode.get('formatted_address', ''))

            addr_comp = regeocode.get('addressComponent', {})
            provinces.append(addr_comp.get('province', ''))
            cities.append(addr_comp.get('city', ''))
            districts.append(addr_comp.get('district', ''))
        else:
            formatted_addresses.append('')
            provinces.append('')
            cities.append('')
            districts.append('')

    df['formatted_address'] = formatted_addresses
    df['province'] = provinces
    df['city'] = cities
    df['district'] = districts
    
    # 保存结果
    try:
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_file}")
        print(f"共处理 {len(df)} 个点")
        
        # 显示前几条结果
        print("\n前5条结果预览:")
        print(df[['id', 'lon', 'lat', 'formatted_address', 'province', 'city', 'district']].head())
        
    except Exception as e:
        print(f"保存文件错误: {e}")


if __name__ == "__main__":
    main()
