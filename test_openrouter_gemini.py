import pandas as pd
import json
import requests
import os
from datetime import datetime

class TestOpenRouterGemini:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.5-flash"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/address-matching",
            "X-Title": "Address Matching Tool",
        }
    
    def load_data(self):
        """加载新城区测试数据"""
        # 加载CSV数据
        self.csv_data = pd.read_csv('积水点_提取字段.csv', encoding='gbk')
        self.excel_data = pd.read_excel('ocr表格1h201.9mm.xlsx')
        
        # 筛选新城区数据
        self.xincheng_csv = self.csv_data[self.csv_data['district'] == '新城区']
        self.xincheng_excel = self.excel_data[self.excel_data['行政区'] == '新城区']
        
        print(f"新城区 CSV数据: {len(self.xincheng_csv)} 条")
        print(f"新城区 Excel数据: {len(self.xincheng_excel)} 条")
    
    def call_openrouter_api(self, prompt: str) -> str:
        """调用OpenRouter Gemini API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=60
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"OpenRouter API调用错误: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            return ""
    
    def create_matching_prompt(self, csv_record, excel_records):
        """创建匹配提示词"""
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 完整地址: {csv_record.get('formatted_address', 'N/A')}
- 行政区: {csv_record['district']}
- 街道办事处: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 街道方向: {csv_record.get('street_number_direction', 'N/A')}
- 道路详情: {csv_record.get('roads_detail', 'N/A')}
- 路口详情: {csv_record.get('roadinters_detail', 'N/A')}

Excel标准风险点列表（{csv_record['district']}）：
"""

        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"

        prompt += """
请综合分析CSV积水点的多维度地址信息与Excel风险点的匹配情况：

匹配分析维度：
1. **完整地址匹配**: 分析formatted_address中的关键地名与Excel风险点的对应关系
2. **主要街道匹配**: 对比street_number_street与Excel风险点中的道路名称
3. **路口交叉匹配**: 分析roadinters_detail中的路口信息与Excel风险点中的交叉口描述
4. **道路详情匹配**: 利用roads_detail中的多条道路信息进行综合判断
5. **方向位置匹配**: 结合street_number_direction和路口方向信息
6. **关键词匹配**: 识别"隧道"、"下穿"、"立交"、"桥"、"十字"等特殊地理标识

匹配策略（按优先级排序）：
- 完整地址中的具体地名与Excel风险点直接匹配（最高优先级）
- 主要街道名称完全匹配
- 路口交叉信息匹配（如"文景路与凤城九路"对应"文景路凤城九路十字"）
- 道路详情中的多条道路信息综合匹配
- 特殊地理标识匹配（隧道、立交等）

请严格按照以下JSON格式返回结果，不要添加任何其他文字：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程，说明使用了哪些维度的信息",
    "matched_elements": ["匹配的关键要素列表，如formatted_address、street_name、intersection等"]
}"""

        return prompt
    
    def test_single_match(self):
        """测试单条记录匹配"""
        excel_records = self.xincheng_excel.to_dict('records')
        
        # 测试第一条记录
        test_record = self.xincheng_csv.iloc[0]
        
        print(f"\n测试记录:")
        print(f"ID: {test_record['id']}")
        print(f"地址: {test_record['township']} {test_record['street_number_street']}")
        print(f"路口: {test_record['roadinters_detail']}")
        
        prompt = self.create_matching_prompt(test_record, excel_records)
        print(f"\n调用OpenRouter Gemini API...")
        
        response = self.call_openrouter_api(prompt)
        
        if response:
            print(f"\nAPI响应:")
            print(response)
            
            try:
                # 清理响应文本
                response = response.strip()
                if response.startswith('```json'):
                    response = response[7:]
                if response.endswith('```'):
                    response = response[:-3]
                response = response.strip()
                
                result = json.loads(response)
                print(f"\n解析结果:")
                print(f"匹配: {result.get('matched', False)}")
                print(f"置信度: {result.get('confidence', 0)}")
                if result.get('matched', False):
                    excel_idx = result['excel_index'] - 1
                    if 0 <= excel_idx < len(excel_records):
                        excel_record = excel_records[excel_idx]
                        print(f"匹配的风险点: {excel_record['编号']} - {excel_record['风险点']}")
                
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
        else:
            print("API调用失败")
    
    def test_api_connection(self):
        """测试API连接"""
        test_prompt = "请回复'连接成功'"
        
        print("测试OpenRouter API连接...")
        response = self.call_openrouter_api(test_prompt)
        
        if response:
            print(f"API连接成功！响应: {response}")
            return True
        else:
            print("API连接失败")
            return False

def main():
    # 获取API密钥
    api_key = None
    
    try:
        import openrouter_config
        if hasattr(openrouter_config, 'OPENROUTER_API_KEY') and openrouter_config.OPENROUTER_API_KEY != "your_openrouter_api_key_here":
            api_key = openrouter_config.OPENROUTER_API_KEY
    except ImportError:
        pass
    
    if not api_key:
        api_key = os.getenv('OPENROUTER_API_KEY')
    
    if not api_key:
        api_key = input("请输入您的OpenRouter API密钥: ").strip()
    
    if not api_key:
        print("错误: 需要提供OpenRouter API密钥")
        return
    
    tester = TestOpenRouterGemini(api_key)
    tester.load_data()
    
    # 先测试API连接
    if tester.test_api_connection():
        # 测试单条记录匹配
        tester.test_single_match()
    else:
        print("请检查API密钥和网络连接")

if __name__ == "__main__":
    main()
