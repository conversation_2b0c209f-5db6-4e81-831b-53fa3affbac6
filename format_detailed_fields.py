#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进一步格式化字段，将roads和roadinters信息分别拆分到多个列中
"""

import pandas as pd
import json
from typing import Dict, Any, List

def extract_detailed_fields_from_api_response(api_response) -> Dict[str, Any]:
    """从API响应中提取详细字段，包括分别的roads和roadinters信息"""
    # 处理空值或NaN
    if pd.isna(api_response) or not api_response or api_response == '':
        return create_empty_fields()
    
    try:
        data = json.loads(api_response)
        regeocode = data.get('regeocode', {})
        addressComponent = regeocode.get('addressComponent', {})
        
        result = {}
        
        # 提取基本字段
        result['formatted_address'] = regeocode.get('formatted_address', '')
        result['district'] = addressComponent.get('district', '')
        result['township'] = addressComponent.get('township', '')
        
        # 提取neighborhood信息
        neighborhood = addressComponent.get('neighborhood', {})
        result['neighborhood_names'] = '; '.join(neighborhood.get('name', []) if isinstance(neighborhood.get('name', []), list) else [])
        result['neighborhood_types'] = '; '.join(neighborhood.get('type', []) if isinstance(neighborhood.get('type', []), list) else [])
        
        # 提取building信息
        building = addressComponent.get('building', {})
        result['building_names'] = '; '.join(building.get('name', []) if isinstance(building.get('name', []), list) else [])
        result['building_types'] = '; '.join(building.get('type', []) if isinstance(building.get('type', []), list) else [])
        
        # 提取streetNumber信息
        streetNumber = addressComponent.get('streetNumber', {})
        result['street_number_street'] = streetNumber.get('street', '') if streetNumber else ''
        result['street_number_number'] = streetNumber.get('number', '') if streetNumber else ''
        result['street_number_direction'] = streetNumber.get('direction', '') if streetNumber else ''
        result['street_number_distance'] = streetNumber.get('distance', '') if streetNumber else ''
        
        # 提取roads信息（最多3条道路）
        roads = regeocode.get('roads', [])
        for i in range(3):
            if i < len(roads):
                road = roads[i]
                result[f'road_{i+1}_name'] = road.get('name', '')
                result[f'road_{i+1}_direction'] = road.get('direction', '')
                result[f'road_{i+1}_distance'] = road.get('distance', '')
                result[f'road_{i+1}_id'] = road.get('id', '')
            else:
                result[f'road_{i+1}_name'] = ''
                result[f'road_{i+1}_direction'] = ''
                result[f'road_{i+1}_distance'] = ''
                result[f'road_{i+1}_id'] = ''
        
        # 提取roadinters信息（最多2个路口）
        roadinters = regeocode.get('roadinters', [])
        for i in range(2):
            if i < len(roadinters):
                inter = roadinters[i]
                result[f'roadinter_{i+1}_first_name'] = inter.get('first_name', '')
                result[f'roadinter_{i+1}_second_name'] = inter.get('second_name', '')
                result[f'roadinter_{i+1}_direction'] = inter.get('direction', '')
                result[f'roadinter_{i+1}_distance'] = inter.get('distance', '')
            else:
                result[f'roadinter_{i+1}_first_name'] = ''
                result[f'roadinter_{i+1}_second_name'] = ''
                result[f'roadinter_{i+1}_direction'] = ''
                result[f'roadinter_{i+1}_distance'] = ''
        
        return result
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return create_empty_fields()

def create_empty_fields() -> Dict[str, Any]:
    """创建空字段字典"""
    result = {
        'formatted_address': '',
        'district': '',
        'township': '',
        'neighborhood_names': '',
        'neighborhood_types': '',
        'building_names': '',
        'building_types': '',
        'street_number_street': '',
        'street_number_number': '',
        'street_number_direction': '',
        'street_number_distance': '',
    }
    
    # 添加roads字段
    for i in range(3):
        result[f'road_{i+1}_name'] = ''
        result[f'road_{i+1}_direction'] = ''
        result[f'road_{i+1}_distance'] = ''
        result[f'road_{i+1}_id'] = ''
    
    # 添加roadinters字段
    for i in range(2):
        result[f'roadinter_{i+1}_first_name'] = ''
        result[f'roadinter_{i+1}_second_name'] = ''
        result[f'roadinter_{i+1}_direction'] = ''
        result[f'roadinter_{i+1}_distance'] = ''
    
    return result

def process_csv_file(input_file: str, output_file: str):
    """处理CSV文件，提取API响应中的详细字段"""
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"读取到 {len(df)} 条记录")
        
        if 'api_response' not in df.columns:
            print("错误: 文件中没有找到 'api_response' 列")
            return
        
        # 提取字段
        extracted_data = []
        error_count = 0
        for index, row in df.iterrows():
            api_response = row.get('api_response', '')
            try:
                fields = extract_detailed_fields_from_api_response(api_response)
                extracted_data.append(fields)
            except Exception as e:
                print(f"处理第 {index + 1} 条记录时出错: {e}")
                error_count += 1
                fields = create_empty_fields()
                extracted_data.append(fields)
            
            if (index + 1) % 50 == 0:
                print(f"已处理 {index + 1} 条记录")
        
        if error_count > 0:
            print(f"处理过程中遇到 {error_count} 个错误")
        
        # 创建新的DataFrame，只保留基本信息和提取的字段
        new_df = pd.DataFrame()
        
        # 保留基本列
        basic_cols = ['lon', 'lat', 'id', 'province', 'city']
        for col in basic_cols:
            if col in df.columns:
                new_df[col] = df[col]
        
        # 添加提取的字段
        for field_name in extracted_data[0].keys():
            new_df[field_name] = [data[field_name] for data in extracted_data]
        
        # 保存结果
        new_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n处理完成，结果保存到: {output_file}")
        
        # 显示统计信息
        print(f"\n字段统计:")
        print(f"- 有district信息的记录: {sum(1 for data in extracted_data if data['district'])}")
        print(f"- 有township信息的记录: {sum(1 for data in extracted_data if data['township'])}")
        print(f"- 有streetNumber信息的记录: {sum(1 for data in extracted_data if data['street_number_street'])}")
        print(f"- 有road_1信息的记录: {sum(1 for data in extracted_data if data['road_1_name'])}")
        print(f"- 有road_2信息的记录: {sum(1 for data in extracted_data if data['road_2_name'])}")
        print(f"- 有road_3信息的记录: {sum(1 for data in extracted_data if data['road_3_name'])}")
        print(f"- 有roadinter_1信息的记录: {sum(1 for data in extracted_data if data['roadinter_1_first_name'])}")
        print(f"- 有roadinter_2信息的记录: {sum(1 for data in extracted_data if data['roadinter_2_first_name'])}")
        
        # 显示前几条结果
        print(f"\n前3条结果预览:")
        display_cols = ['id', 'district', 'township', 'road_1_name', 'road_2_name', 'roadinter_1_first_name']
        available_cols = [col for col in display_cols if col in new_df.columns]
        print(new_df[available_cols].head(3))
        
        # 显示列名
        print(f"\n输出文件包含的列 (共{len(new_df.columns)}列):")
        for i, col in enumerate(new_df.columns, 1):
            print(f"{i:2d}. {col}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    input_file = "积水点_带地址.csv"
    output_file = "积水点_详细字段.csv"
    
    print("高德地图API详细字段提取工具")
    print("提取字段: district、township、neighborhood、building、streetNumber、roads(分别)、roadinters(分别)")
    print()
    
    # 处理文件
    process_csv_file(input_file, output_file)
    
    print(f"\n处理完成！")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"\n说明:")
    print(f"- roads信息分为3列: road_1, road_2, road_3")
    print(f"- roadinters信息分为2列: roadinter_1, roadinter_2")
    print(f"- 每个road包含: name, direction, distance, id")
    print(f"- 每个roadinter包含: first_name, second_name, direction, distance")
