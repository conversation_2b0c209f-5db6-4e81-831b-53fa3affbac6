import pandas as pd
import json

def analyze_sample_data():
    """分析样本数据，准备匹配测试"""
    
    # 加载数据
    csv_data = pd.read_csv('积水点_带地址.csv', encoding='utf-8')
    weiyangqu_csv = csv_data[csv_data['district'] == '未央区'].copy()
    
    excel_data = pd.read_csv('excel_data.csv', encoding='utf-8')
    weiyangqu_excel = excel_data[excel_data['行政区'] == '未央区'].copy()
    
    print("=== 数据概览 ===")
    print(f"CSV未央区数据: {len(weiyangqu_csv)} 条")
    print(f"Excel未央区数据: {len(weiyangqu_excel)} 条")
    
    print("\n=== CSV样本数据（前5条）===")
    for idx, row in weiyangqu_csv.head(5).iterrows():
        try:
            api_response = json.loads(row['api_response'])
            formatted_address = api_response['regeocode']['formatted_address']
            roads = api_response['regeocode']['roads'][:2]  # 前2条道路
            print(f"\nID {row['id']}:")
            print(f"  地址: {formatted_address}")
            print(f"  主要道路: {[road['name'] for road in roads]}")
        except:
            print(f"\nID {row['id']}:")
            print(f"  地址: {row['formatted_address']}")
    
    print("\n=== Excel样本数据（前10条）===")
    for idx, row in weiyangqu_excel.head(10).iterrows():
        print(f"{row['编号']}: {row['风险点']} ({row['风险等级']})")
    
    print("\n=== 手动匹配示例 ===")
    # 让我们手动分析几个可能的匹配
    
    # CSV ID 1: 陕西省西安市未央区汉城街道文景路海珀兰轩
    csv_1 = weiyangqu_csv[weiyangqu_csv['id'] == 1].iloc[0]
    api_1 = json.loads(csv_1['api_response'])
    roads_1 = [road['name'] for road in api_1['regeocode']['roads'][:3]]
    
    print(f"\nCSV ID 1 分析:")
    print(f"  地址: {api_1['regeocode']['formatted_address']}")
    print(f"  主要道路: {roads_1}")
    
    # 查找Excel中包含"文景路"的记录
    excel_wenjing = weiyangqu_excel[weiyangqu_excel['风险点'].str.contains('文景', na=False)]
    print(f"  Excel中包含'文景'的记录:")
    for _, row in excel_wenjing.iterrows():
        print(f"    {row['编号']}: {row['风险点']}")
    
    # CSV ID 2: 陕西省西安市未央区张家堡街道凤城九路中登文景大厦
    csv_2 = weiyangqu_csv[weiyangqu_csv['id'] == 2].iloc[0]
    api_2 = json.loads(csv_2['api_response'])
    roads_2 = [road['name'] for road in api_2['regeocode']['roads'][:3]]
    
    print(f"\nCSV ID 2 分析:")
    print(f"  地址: {api_2['regeocode']['formatted_address']}")
    print(f"  主要道路: {roads_2}")
    
    # 查找Excel中包含"凤城九路"的记录
    excel_fengcheng = weiyangqu_excel[weiyangqu_excel['风险点'].str.contains('凤城九路', na=False)]
    print(f"  Excel中包含'凤城九路'的记录:")
    for _, row in excel_fengcheng.iterrows():
        print(f"    {row['编号']}: {row['风险点']}")
    
    return weiyangqu_csv, weiyangqu_excel

def create_matching_prompt_example():
    """创建一个匹配提示词示例"""
    
    weiyangqu_csv, weiyangqu_excel = analyze_sample_data()
    
    # 选择第一条CSV记录
    csv_record = weiyangqu_csv.iloc[0]
    api_response = json.loads(csv_record['api_response'])
    
    # 选择前10条Excel记录作为候选
    excel_records = weiyangqu_excel.head(10).to_dict('records')
    
    prompt = f"""
你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 标准地址: {api_response['regeocode']['formatted_address']}
- 主要道路: {[road['name'] for road in api_response['regeocode']['roads'][:3]]}
- 路口信息: {[inter.get('first_name', '') + '与' + inter.get('second_name', '') for inter in api_response['regeocode']['roadinters'][:2]]}

Excel标准风险点列表：
"""
    
    for i, excel_record in enumerate(excel_records, 1):
        prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
    
    prompt += """
请分析CSV积水点的地址信息，判断它是否与Excel列表中的某个风险点匹配。

匹配规则：
1. 优先匹配道路名称和交叉口
2. 考虑地理位置的相近性  
3. 注意下穿、立交、桥下等关键词
4. 允许地址描述的不同表达方式

请返回JSON格式的结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "匹配或不匹配的详细原因"
}
"""
    
    print("\n=== 生成的匹配提示词 ===")
    print(prompt)
    
    # 保存提示词到文件
    with open('matching_prompt_example.txt', 'w', encoding='utf-8') as f:
        f.write(prompt)
    
    print(f"\n提示词已保存到: matching_prompt_example.txt")

if __name__ == "__main__":
    create_matching_prompt_example()
