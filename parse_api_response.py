#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析高德地图API响应数据，提取详细地址信息
"""

import pandas as pd
import json
from typing import Dict, List, Any

def extract_road_info(regeocode: Dict[str, Any]) -> str:
    """提取道路信息"""
    roads = regeocode.get('roads', [])
    if not roads:
        return ""
    
    road_descriptions = []
    
    # 按距离排序
    sorted_roads = sorted(roads, key=lambda x: float(x.get('distance', 999)))
    
    # 获取最近的几条道路
    nearby_roads = []
    special_roads = []
    
    for road in sorted_roads[:5]:
        road_name = road.get('name', '').strip()
        distance = float(road.get('distance', 999))
        direction = road.get('direction', '')
        
        if road_name and distance < 300:
            # 检查特殊道路类型
            special_types = ['辅路', '立交', '桥', '隧道', '下穿', '上跨', '匝道', '环路', '高架']
            if any(stype in road_name for stype in special_types):
                special_roads.append({
                    'name': road_name,
                    'distance': distance,
                    'direction': direction
                })
            else:
                nearby_roads.append({
                    'name': road_name,
                    'distance': distance,
                    'direction': direction
                })
    
    # 构建道路描述
    if len(nearby_roads) >= 2:
        # 交叉路口
        road1 = nearby_roads[0]['name']
        road2 = nearby_roads[1]['name']
        road_descriptions.append(f"{road1}与{road2}交叉口")
        
        # 添加特殊道路信息
        if special_roads:
            road_descriptions.append(f"({special_roads[0]['name']})")
    elif len(nearby_roads) == 1:
        # 单条道路 + 方向
        main_road = nearby_roads[0]
        direction_map = {
            '东': '东侧', '西': '西侧', '南': '南侧', '北': '北侧',
            'east': '东侧', 'west': '西侧', 'south': '南侧', 'north': '北侧'
        }
        direction_desc = direction_map.get(main_road['direction'], '')
        
        if direction_desc and main_road['distance'] > 30:
            road_descriptions.append(f"{main_road['name']}{direction_desc}")
        else:
            road_descriptions.append(main_road['name'])
        
        # 添加特殊道路信息
        if special_roads:
            road_descriptions.append(f"({special_roads[0]['name']})")
    elif special_roads:
        # 只有特殊道路
        road_descriptions.append(special_roads[0]['name'])
    
    return ''.join(road_descriptions)

def extract_poi_info(regeocode: Dict[str, Any]) -> str:
    """提取POI信息"""
    pois = regeocode.get('pois', [])
    if not pois:
        return ""
    
    # 筛选重要POI
    important_pois = []
    for poi in pois[:10]:
        poi_name = poi.get('name', '').strip()
        poi_type = poi.get('type', '').strip()
        distance = float(poi.get('distance', 999))
        
        if poi_name and distance < 200:  # 200米内
            # 过滤不需要的POI
            skip_types = ['停车场', '公厕', 'ATM', '收费站', '加油站']
            if not any(skip in poi_name for skip in skip_types):
                # 优先重要POI类型
                priority_types = ['住宅区', '小区', '学校', '医院', '商场', '写字楼', '酒店']
                is_priority = any(ptype in poi_type or ptype in poi_name for ptype in priority_types)
                
                important_pois.append({
                    'name': poi_name,
                    'type': poi_type,
                    'distance': distance,
                    'is_priority': is_priority
                })
    
    # 按优先级和距离排序
    important_pois.sort(key=lambda x: (not x['is_priority'], x['distance']))
    
    if important_pois:
        return important_pois[0]['name']
    
    return ""

def extract_detailed_info(api_response: str) -> Dict[str, Any]:
    """从API响应中提取详细信息"""
    if not api_response:
        return {
            'formatted_address': '',
            'province': '',
            'city': '',
            'district': '',
            'township': '',
            'street': '',
            'road_info': '',
            'poi_info': '',
            'roads_count': 0,
            'pois_count': 0,
            'roads_detail': '',
            'pois_detail': ''
        }
    
    try:
        data = json.loads(api_response)
        regeocode = data.get('regeocode', {})
        
        # 基本地址信息
        formatted_address = regeocode.get('formatted_address', '')
        addressComponent = regeocode.get('addressComponent', {})
        
        province = addressComponent.get('province', '')
        city = addressComponent.get('city', '')
        district = addressComponent.get('district', '')
        township = addressComponent.get('township', '')
        
        # 街道信息
        streetNumber = addressComponent.get('streetNumber', {})
        street = streetNumber.get('street', '') if streetNumber else ''
        
        # 道路和POI信息
        roads = regeocode.get('roads', [])
        pois = regeocode.get('pois', [])
        
        # 提取道路和POI描述
        road_info = extract_road_info(regeocode)
        poi_info = extract_poi_info(regeocode)
        
        # 详细的道路和POI列表
        roads_detail = '; '.join([f"{road.get('name', '')}({road.get('distance', '')}m,{road.get('direction', '')})" 
                                 for road in roads[:5] if road.get('name')])
        pois_detail = '; '.join([f"{poi.get('name', '')}({poi.get('distance', '')}m)" 
                                for poi in pois[:5] if poi.get('name')])
        
        return {
            'formatted_address': formatted_address,
            'province': province,
            'city': city,
            'district': district,
            'township': township,
            'street': street,
            'road_info': road_info,
            'poi_info': poi_info,
            'roads_count': len(roads),
            'pois_count': len(pois),
            'roads_detail': roads_detail,
            'pois_detail': pois_detail
        }
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return {
            'formatted_address': '',
            'province': '',
            'city': '',
            'district': '',
            'township': '',
            'street': '',
            'road_info': '',
            'poi_info': '',
            'roads_count': 0,
            'pois_count': 0,
            'roads_detail': '',
            'pois_detail': ''
        }

def parse_api_responses(input_file: str, output_file: str):
    """解析API响应文件"""
    try:
        # 读取包含API响应的CSV文件
        df = pd.read_csv(input_file)
        print(f"读取到 {len(df)} 条记录")
        
        if 'api_response' not in df.columns:
            print("错误: 文件中没有找到 'api_response' 列")
            return
        
        # 解析每个API响应
        detailed_info = []
        for index, row in df.iterrows():
            api_response = row.get('api_response', '')
            info = extract_detailed_info(api_response)
            detailed_info.append(info)
            
            if (index + 1) % 50 == 0:
                print(f"已处理 {index + 1} 条记录")
        
        # 将解析结果添加到DataFrame
        for key in detailed_info[0].keys():
            df[key] = [info[key] for info in detailed_info]
        
        # 构建两种地址格式
        road_addresses = []
        poi_addresses = []
        
        for _, row in df.iterrows():
            # 道路地址
            road_addr = f"{row['province']}{row['city']}{row['district']}{row['road_info']}"
            road_addresses.append(road_addr if road_addr.strip() else row['formatted_address'])
            
            # POI地址
            if row['poi_info'] and row['road_info']:
                # 提取主要道路名
                main_road = row['road_info']
                if '与' in main_road and '交叉口' in main_road:
                    main_road = main_road.split('与')[0]
                elif any(side in main_road for side in ['东侧', '西侧', '南侧', '北侧']):
                    for side in ['东侧', '西侧', '南侧', '北侧']:
                        main_road = main_road.replace(side, '')
                
                poi_addr = f"{row['province']}{row['city']}{row['district']}{main_road}{row['poi_info']}"
                poi_addresses.append(poi_addr)
            else:
                poi_addresses.append('')
        
        df['road_address'] = road_addresses
        df['poi_address'] = poi_addresses
        
        # 保存结果
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n解析完成，结果保存到: {output_file}")
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"- 有道路信息的记录: {sum(1 for addr in road_addresses if addr.strip())}")
        print(f"- 有POI信息的记录: {sum(1 for addr in poi_addresses if addr.strip())}")
        
        # 显示前几条结果
        print(f"\n前5条结果预览:")
        display_cols = ['id', 'formatted_address', 'road_address', 'poi_address']
        print(df[display_cols].head())
        
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    input_file = "积水点_带地址.csv"  # 包含API响应的文件
    output_file = "积水点_详细地址.csv"  # 解析后的文件
    
    parse_api_responses(input_file, output_file)
