import pandas as pd
import json
import requests
import time
from typing import List, Dict, Any

class AddressMatcher:
    def __init__(self, openrouter_api_key: str):
        self.api_key = openrouter_api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载CSV数据（积水点带地址）
        self.csv_data = pd.read_csv('积水点_带地址.csv', encoding='utf-8')
        self.weiyangqu_csv = self.csv_data[self.csv_data['district'] == '未央区'].copy()
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_csv('excel_data.csv', encoding='utf-8')
        self.weiyangqu_excel = self.excel_data[self.excel_data['行政区'] == '未央区'].copy()
        
        print(f"CSV未央区数据: {len(self.weiyangqu_csv)} 条")
        print(f"Excel未央区数据: {len(self.weiyangqu_excel)} 条")
    
    def call_llm(self, prompt: str, model: str = "anthropic/claude-3.5-sonnet") -> str:
        """调用OpenRouter API"""
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建地址匹配的提示词"""
        # 解析CSV记录中的API响应以获取详细地址信息
        try:
            api_response = json.loads(csv_record['api_response'])
            formatted_address = api_response['regeocode']['formatted_address']
            roads = api_response['regeocode']['roads']
            roadinters = api_response['regeocode']['roadinters']
        except:
            formatted_address = csv_record['formatted_address']
            roads = []
            roadinters = []
        
        prompt = f"""
你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 标准地址: {formatted_address}
- 道路信息: {roads[:3] if roads else '无'}  # 只显示前3条道路信息
- 路口信息: {roadinters[:2] if roadinters else '无'}  # 只显示前2条路口信息

Excel标准风险点列表：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"""
{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}
"""
        
        prompt += """
请分析CSV积水点的地址信息，判断它是否与Excel列表中的某个风险点匹配。

匹配规则：
1. 优先匹配道路名称和交叉口
2. 考虑地理位置的相近性
3. 注意下穿、立交、桥下等关键词
4. 允许地址描述的不同表达方式

请返回JSON格式的结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1)，
    "reason": "匹配或不匹配的详细原因"
}
"""
        return prompt
    
    def match_addresses(self, batch_size: int = 10):
        """批量匹配地址"""
        results = []
        excel_records = self.weiyangqu_excel.to_dict('records')
        
        for idx, csv_record in self.weiyangqu_csv.iterrows():
            print(f"\n处理CSV记录 {csv_record['id']}: {csv_record['formatted_address']}")
            
            # 创建匹配提示词
            prompt = self.create_matching_prompt(csv_record, excel_records)
            
            # 调用LLM
            response = self.call_llm(prompt)
            
            try:
                # 解析JSON响应
                result = json.loads(response.strip())
                result['csv_id'] = csv_record['id']
                result['csv_address'] = csv_record['formatted_address']
                results.append(result)
                
                if result['matched']:
                    excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']} (置信度: {result['confidence']})")
                else:
                    print(f"✗ 未找到匹配")
                    
            except json.JSONDecodeError:
                print(f"JSON解析错误: {response}")
                results.append({
                    'csv_id': csv_record['id'],
                    'csv_address': csv_record['formatted_address'],
                    'matched': False,
                    'excel_index': None,
                    'excel_code': None,
                    'confidence': 0,
                    'reason': f'JSON解析错误: {response}'
                })
            
            # 添加延迟避免API限制
            time.sleep(1)
        
        return results
    
    def save_results(self, results: List[Dict]):
        """保存匹配结果"""
        df_results = pd.DataFrame(results)
        df_results.to_csv('address_matching_results.csv', index=False, encoding='utf-8')
        
        # 统计匹配情况
        matched_count = sum(1 for r in results if r['matched'])
        total_count = len(results)
        
        print(f"\n匹配完成！")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"结果已保存到: address_matching_results.csv")

def main():
    # 请在这里输入您的OpenRouter API密钥
    api_key = input("请输入您的OpenRouter API密钥: ").strip()
    
    if not api_key:
        print("错误: 需要提供OpenRouter API密钥")
        return
    
    matcher = AddressMatcher(api_key)
    matcher.load_data()
    
    # 先处理前5条数据作为测试
    print("\n开始处理前5条数据作为测试...")
    matcher.weiyangqu_csv = matcher.weiyangqu_csv.head(5)
    
    results = matcher.match_addresses()
    matcher.save_results(results)

if __name__ == "__main__":
    main()
