import pandas as pd
import json
import requests
import time
from typing import List, Dict, Any
import config

class ZhipuAddressMatcher:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.base_url = config.BASE_URL
        self.model = config.MODEL_NAME
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载CSV数据（积水点带地址）
        self.csv_data = pd.read_csv(config.CSV_FILE, encoding='utf-8')
        self.weiyangqu_csv = self.csv_data[self.csv_data['district'] == '未央区'].copy()
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel(config.EXCEL_FILE)
        self.weiyangqu_excel = self.excel_data[self.excel_data['行政区'] == '未央区'].copy()
        
        print(f"CSV未央区数据: {len(self.weiyangqu_csv)} 条")
        print(f"Excel未央区数据: {len(self.weiyangqu_excel)} 条")
        
        if config.ENABLE_PREVIEW:
            print(f"预览模式：只处理前 {config.PREVIEW_COUNT} 条CSV数据")
            self.weiyangqu_csv = self.weiyangqu_csv.head(config.PREVIEW_COUNT)
    
    def call_zhipu_api(self, prompt: str) -> str:
        """调用智谱AI API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=config.TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建地址匹配的提示词"""
        # 解析CSV记录中的API响应以获取详细地址信息
        try:
            api_response = json.loads(csv_record['api_response'])
            formatted_address = api_response['regeocode']['formatted_address']
            roads = api_response['regeocode']['roads'][:3]  # 前3条道路
            roadinters = api_response['regeocode']['roadinters'][:2]  # 前2个路口
            
            road_names = [road['name'] for road in roads]
            intersection_info = []
            for inter in roadinters:
                first_name = inter.get('first_name', '')
                second_name = inter.get('second_name', '')
                if first_name and second_name:
                    intersection_info.append(f"{first_name}与{second_name}")
        except:
            formatted_address = csv_record['formatted_address']
            road_names = []
            intersection_info = []
        
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 标准地址: {formatted_address}
- 主要道路: {road_names}
- 路口信息: {intersection_info}

Excel标准风险点列表：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请分析CSV积水点的地址信息，判断它是否与Excel列表中的某个风险点匹配。

匹配规则：
1. 优先匹配道路名称和交叉口
2. 考虑地理位置的相近性
3. 注意下穿、立交、桥下等关键词
4. 允许地址描述的不同表达方式

请严格按照以下JSON格式返回结果，不要添加任何其他文字：
{
    "matched": true,
    "excel_index": 9,
    "excel_code": "XA58",
    "confidence": 0.85,
    "reason": "CSV积水点位于文景路，与Excel第9条记录'文景路立交'高度匹配"
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        print(f"\n处理CSV记录 {csv_record['id']}: {csv_record['formatted_address']}")
        
        # 创建匹配提示词
        prompt = self.create_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_zhipu_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            result['csv_id'] = csv_record['id']
            result['csv_address'] = csv_record['formatted_address']
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']} (置信度: {result.get('confidence', 0)})")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"原始响应: {response}")
            result = {
                'csv_id': csv_record['id'],
                'csv_address': csv_record['formatted_address'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}'
            }
        
        return result
    
    def match_addresses(self):
        """批量匹配地址"""
        results = []
        excel_records = self.weiyangqu_excel.to_dict('records')
        
        for idx, csv_record in self.weiyangqu_csv.iterrows():
            result = self.match_single_record(csv_record, excel_records)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(config.DELAY_BETWEEN_CALLS)
        
        return results
    
    def save_results(self, results: List[Dict]):
        """保存匹配结果"""
        df_results = pd.DataFrame(results)
        
        # 保存详细结果
        df_results.to_csv('address_matching_results.csv', index=False, encoding='utf-8')
        
        # 创建匹配成功的汇总
        matched_results = [r for r in results if r.get('matched', False)]
        if matched_results:
            df_matched = pd.DataFrame(matched_results)
            df_matched.to_csv('matched_results_summary.csv', index=False, encoding='utf-8')
        
        # 统计匹配情况
        matched_count = len(matched_results)
        total_count = len(results)
        
        print(f"\n=== 匹配完成 ===")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"详细结果已保存到: address_matching_results.csv")
        if matched_results:
            print(f"匹配成功汇总已保存到: matched_results_summary.csv")
        
        # 显示匹配成功的记录
        if matched_results:
            print(f"\n=== 匹配成功的记录 ===")
            for result in matched_results:
                print(f"CSV ID {result['csv_id']}: {result['excel_code']} (置信度: {result.get('confidence', 0)})")

def main():
    matcher = ZhipuAddressMatcher()
    matcher.load_data()
    
    print("\n开始地址匹配...")
    results = matcher.match_addresses()
    matcher.save_results(results)

if __name__ == "__main__":
    main()
