import pandas as pd

# 尝试不同编码读取
encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp936']

for encoding in encodings:
    try:
        df = pd.read_csv('积水点_提取字段1.csv', encoding=encoding)
        print(f'使用 {encoding} 编码成功读取')
        print(f'列名: {df.columns.tolist()}')
        print(f'数据形状: {df.shape}')
        print(f'前3行数据:')
        for i, row in df.head(3).iterrows():
            print(f'  行{i}: district={row["district"]}, township={row["township"]}')
        
        # 筛选新城区数据
        xincheng_data = df[df['district'] == '新城区']
        print(f'\n新城区数据: {len(xincheng_data)} 条')
        if len(xincheng_data) > 0:
            print('新城区前3条数据:')
            for i, row in xincheng_data.head(3).iterrows():
                print(f'  ID {row["id"]}: {row["township"]} {row["street_number_street"]}')
        break
    except Exception as e:
        print(f'使用 {encoding} 编码失败: {str(e)[:100]}')
