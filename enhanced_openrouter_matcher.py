import pandas as pd
import json
import requests
import time
import os
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import pickle
from datetime import datetime
from typing import List, Dict, Any
import threading
import traceback

class EnhancedOpenRouterMatcher:
    def __init__(self, api_key: str, max_workers=3, max_retries=3):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.5-flash"
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        self.cache_file = "enhanced_openrouter_cache.pkl"
        self.results_cache = self.load_cache()
        self.cache_lock = threading.Lock()
        self.processed_count = 0
        self.total_count = 0
    
    def load_cache(self):
        """加载缓存数据"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                print(f"✓ 已加载缓存数据: {len(cache)} 条记录")
                return cache
            except Exception as e:
                print(f"⚠ 加载缓存失败: {e}")
                return {}
        return {}
    
    def save_cache(self):
        """保存缓存数据"""
        try:
            with self.cache_lock:
                with open(self.cache_file, 'wb') as f:
                    pickle.dump(self.results_cache, f)
                print(f"✓ 已保存缓存数据: {len(self.results_cache)} 条记录")
        except Exception as e:
            print(f"⚠ 保存缓存失败: {e}")
    
    def load_data(self):
        """加载CSV和Excel数据"""
        try:
            # 加载CSV数据
            self.csv_data = pd.read_csv('积水点_提取字段.csv', encoding='gbk')
            # 加载Excel数据
            self.excel_data = pd.read_excel('ocr表格1h201.9mm.xlsx')
            
            print(f"✓ CSV总数据: {len(self.csv_data)} 条")
            print(f"✓ Excel总数据: {len(self.excel_data)} 条")
            
            # 显示各行政区的数据分布
            print(f"\nCSV数据行政区分布:")
            csv_districts = self.csv_data['district'].value_counts()
            for district, count in csv_districts.items():
                print(f"  {district}: {count} 条")
            
            print(f"\nExcel数据行政区分布:")
            excel_districts = self.excel_data['行政区'].value_counts()
            for district, count in excel_districts.items():
                print(f"  {district}: {count} 条")
                
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def call_openrouter_api_with_retry(self, prompt: str, csv_id: int) -> str:
        """带重试机制的API调用"""
        for attempt in range(self.max_retries):
            try:
                payload = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "user",
                            "content": [{"type": "text", "text": prompt}]
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.5
                }
                
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    data=json.dumps(payload),
                    timeout=60
                )
                response.raise_for_status()
                result = response.json()
                return result['choices'][0]['message']['content']
                
            except Exception as e:
                print(f"⚠ CSV ID {csv_id} API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)[:100]}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    print(f"❌ CSV ID {csv_id} 所有重试均失败")
                    return ""
        return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建匹配提示词"""
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 完整地址: {csv_record.get('formatted_address', 'N/A')}
- 行政区: {csv_record['district']}
- 街道办事处: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 街道方向: {csv_record.get('street_number_direction', 'N/A')}
- 道路详情: {csv_record.get('roads_detail', 'N/A')}
- 路口详情: {csv_record.get('roadinters_detail', 'N/A')}

Excel标准风险点列表（{csv_record['district']}）：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请综合分析CSV积水点的多维度地址信息与Excel风险点的匹配情况：

匹配分析维度：
1. **完整地址匹配**: 分析formatted_address中的关键地名与Excel风险点的对应关系
2. **主要街道匹配**: 对比street_number_street与Excel风险点中的道路名称
3. **路口交叉匹配**: 分析roadinters_detail中的路口信息与Excel风险点中的交叉口描述
4. **道路详情匹配**: 利用roads_detail中的多条道路信息进行综合判断
5. **方向位置匹配**: 结合street_number_direction和路口方向信息
6. **关键词匹配**: 识别"隧道"、"下穿"、"立交"、"桥"、"十字"等特殊地理标识

匹配策略（按优先级排序）：
- 完整地址中的具体地名与Excel风险点直接匹配（最高优先级）
- 主要街道名称完全匹配
- 路口交叉信息匹配
- 道路详情中的多条道路信息综合匹配
- 特殊地理标识匹配（隧道、立交等）

请严格按照以下JSON格式返回结果，不要添加任何其他文字：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程",
    "matched_elements": ["匹配的关键要素列表"]
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        csv_id = csv_record['id']
        
        # 检查缓存
        with self.cache_lock:
            if csv_id in self.results_cache:
                print(f"📋 从缓存获取 CSV ID {csv_id}")
                return self.results_cache[csv_id]
        
        print(f"🔄 处理CSV记录 ID {csv_id} ({csv_record['district']})")
        
        # 创建匹配提示词
        prompt = self.create_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_openrouter_api_with_retry(prompt, csv_id)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            
            # 添加CSV记录信息
            result.update({
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record.get('roadinters_detail', 'N/A'),
                'csv_formatted_address': csv_record.get('formatted_address', 'N/A'),
                'csv_roads_detail': csv_record.get('roads_detail', 'N/A'),
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat']
            })
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✅ 匹配成功: {excel_record['编号']} - {excel_record['风险点']} (置信度: {result.get('confidence', 0)})")
                else:
                    print(f"❌ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"❌ 未找到匹配")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            result = {
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record.get('roadinters_detail', 'N/A'),
                'csv_formatted_address': csv_record.get('formatted_address', 'N/A'),
                'csv_roads_detail': csv_record.get('roads_detail', 'N/A'),
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_elements': []
            }
        
        # 保存到缓存
        with self.cache_lock:
            self.results_cache[csv_id] = result
            self.processed_count += 1
            
            # 每处理5条记录保存一次缓存
            if self.processed_count % 5 == 0:
                self.save_cache()
                print(f"📊 进度: {self.processed_count}/{self.total_count} ({self.processed_count/self.total_count*100:.1f}%)")
        
        return result
