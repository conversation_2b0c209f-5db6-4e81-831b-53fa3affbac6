import pandas as pd
import json
import requests
import time
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import pickle
from datetime import datetime
from typing import List, Dict, Any

class OpenRouterGeminiMatcher:
    def __init__(self, api_key: str, max_workers=3):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.5-flash"
        self.max_workers = max_workers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        self.cache_file = "openrouter_matching_cache.pkl"
        self.results_cache = self.load_cache()
    
    def load_cache(self):
        """加载缓存数据"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                print(f"已加载缓存数据: {len(cache)} 条记录")
                return cache
            except Exception as e:
                print(f"加载缓存失败: {e}")
                return {}
        return {}
    
    def save_cache(self):
        """保存缓存数据"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.results_cache, f)
            print(f"已保存缓存数据: {len(self.results_cache)} 条记录")
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载CSV数据（积水点提取字段）
        self.csv_data = pd.read_csv('积水点_提取字段.csv', encoding='gbk')
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel('ocr表格1h201.9mm.xlsx')
        
        print(f"CSV总数据: {len(self.csv_data)} 条")
        print(f"Excel总数据: {len(self.excel_data)} 条")
        
        # 显示各行政区的数据分布
        print(f"\nCSV数据行政区分布:")
        csv_districts = self.csv_data['district'].value_counts()
        for district, count in csv_districts.items():
            print(f"  {district}: {count} 条")
        
        print(f"\nExcel数据行政区分布:")
        excel_districts = self.excel_data['行政区'].value_counts()
        for district, count in excel_districts.items():
            print(f"  {district}: {count} 条")
    
    def call_openrouter_api(self, prompt: str) -> str:
        """调用OpenRouter Gemini API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=60
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"OpenRouter API调用错误: {e}")
            return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建匹配提示词"""

        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 完整地址: {csv_record.get('formatted_address', 'N/A')}
- 行政区: {csv_record['district']}
- 街道办事处: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 街道方向: {csv_record.get('street_number_direction', 'N/A')}
- 道路详情: {csv_record.get('roads_detail', 'N/A')}
- 路口详情: {csv_record.get('roadinters_detail', 'N/A')}

Excel标准风险点列表（{csv_record['district']}）：
"""

        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"

        prompt += """
请综合分析CSV积水点的多维度地址信息与Excel风险点的匹配情况：

匹配分析维度：
1. **完整地址匹配**: 分析formatted_address中的关键地名与Excel风险点的对应关系
2. **主要街道匹配**: 对比street_number_street与Excel风险点中的道路名称
3. **路口交叉匹配**: 分析roadinters_detail中的路口信息与Excel风险点中的交叉口描述
4. **道路详情匹配**: 利用roads_detail中的多条道路信息进行综合判断
5. **方向位置匹配**: 结合street_number_direction和路口方向信息
6. **关键词匹配**: 识别"隧道"、"下穿"、"立交"、"桥"、"十字"等特殊地理标识
7. **街道办匹配**: 利用township信息辅助定位判断

匹配策略（按优先级排序）：
- 完整地址中的具体地名与Excel风险点直接匹配（最高优先级）
- 主要街道名称完全匹配
- 路口交叉信息匹配（如"文景路与凤城九路"对应"文景路凤城九路十字"）
- 道路详情中的多条道路信息综合匹配
- 特殊地理标识匹配（隧道、立交等）
- 避免过于宽泛的匹配，确保地理位置的准确性

分析示例：
如果CSV记录显示"文景路与凤城九路路口"，应该匹配Excel中的"文景路与凤城九路十字"或类似描述。
如果roads_detail显示多条相关道路，应综合考虑这些道路信息。

请严格按照以下JSON格式返回结果，不要添加任何其他文字：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程，说明使用了哪些维度的信息",
    "matched_elements": ["匹配的关键要素列表，如formatted_address、street_name、intersection等"]
}"""

        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        csv_id = csv_record['id']
        
        # 检查缓存
        if csv_id in self.results_cache:
            print(f"从缓存获取 CSV ID {csv_id}")
            return self.results_cache[csv_id]
        
        print(f"处理CSV记录 ID {csv_id} ({csv_record['district']})")
        
        # 创建匹配提示词
        prompt = self.create_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_openrouter_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            
            # 添加CSV记录信息
            result.update({
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record.get('roadinters_detail', 'N/A'),
                'csv_formatted_address': csv_record.get('formatted_address', 'N/A'),
                'csv_roads_detail': csv_record.get('roads_detail', 'N/A'),
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat']
            })
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']}")
                    print(f"  置信度: {result.get('confidence', 0)}")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"原始响应: {response[:200]}...")
            result = {
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record.get('roadinters_detail', 'N/A'),
                'csv_formatted_address': csv_record.get('formatted_address', 'N/A'),
                'csv_roads_detail': csv_record.get('roads_detail', 'N/A'),
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_elements': []
            }
        
        # 保存到缓存
        self.results_cache[csv_id] = result
        return result
    
    def match_district_concurrent(self, district: str):
        """并发匹配单个行政区的数据"""
        print(f"\n{'='*50}")
        print(f"开始并发处理 {district}")
        print(f"{'='*50}")
        
        # 筛选当前行政区的数据
        district_csv = self.csv_data[self.csv_data['district'] == district]
        district_excel = self.excel_data[self.excel_data['行政区'] == district]
        
        print(f"{district} CSV数据: {len(district_csv)} 条")
        print(f"{district} Excel数据: {len(district_excel)} 条")
        
        if len(district_excel) == 0:
            print(f"警告: {district} 没有Excel标准数据，跳过")
            return []
        
        excel_records = district_excel.to_dict('records')
        csv_records = district_csv.to_dict('records')
        
        # 使用线程池并发处理
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_record = {
                executor.submit(self.match_single_record, csv_record, excel_records): csv_record
                for csv_record in csv_records
            }
            
            # 收集结果
            for future in as_completed(future_to_record):
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 每处理5条记录保存一次缓存
                    if len(results) % 5 == 0:
                        self.save_cache()
                        print(f"已处理 {len(results)}/{len(csv_records)} 条记录")
                        
                except Exception as e:
                    csv_record = future_to_record[future]
                    print(f"处理CSV记录 {csv_record['id']} 时出错: {e}")
                
                # 添加延迟避免API限制
                time.sleep(0.5)
        
        return results
    
    def match_all_districts(self):
        """匹配所有行政区的数据"""
        all_results = []
        
        # 获取所有行政区
        districts = self.csv_data['district'].unique()
        
        for district in districts:
            district_results = self.match_district_concurrent(district)
            all_results.extend(district_results)
            
            # 每个行政区处理完后保存缓存
            self.save_cache()
            print(f"{district} 处理完成，累计结果: {len(all_results)} 条")
        
        return all_results

    def save_to_excel(self, results: List[Dict]):
        """将结果保存到Excel文件"""
        print(f"\n开始保存结果到Excel文件...")

        # 创建结果DataFrame
        df_results = pd.DataFrame(results)

        # 读取原始Excel文件
        original_excel = pd.read_excel('ocr表格1h201.9mm.xlsx')

        # 为每个Excel记录添加匹配信息
        enhanced_excel = original_excel.copy()

        # 初始化新列
        new_columns = ['经度', '纬度', 'csv_id', '置信度', 'reason', 'matched_elements',
                      'csv_district', 'csv_street', 'csv_intersection', 'csv_formatted_address', 'csv_roads_detail']
        for col in new_columns:
            enhanced_excel[col] = None

        # 填充匹配成功的记录
        matched_results = df_results[df_results['matched'] == True]

        for _, result in matched_results.iterrows():
            excel_code = result['excel_code']
            if excel_code:
                # 找到对应的Excel记录
                excel_mask = enhanced_excel['编号'] == excel_code
                if excel_mask.any():
                    enhanced_excel.loc[excel_mask, '经度'] = result['longitude']
                    enhanced_excel.loc[excel_mask, '纬度'] = result['latitude']
                    enhanced_excel.loc[excel_mask, 'csv_id'] = result['csv_id']
                    enhanced_excel.loc[excel_mask, '置信度'] = result['confidence']
                    enhanced_excel.loc[excel_mask, 'reason'] = result['reason']
                    enhanced_excel.loc[excel_mask, 'matched_elements'] = str(result['matched_elements'])
                    enhanced_excel.loc[excel_mask, 'csv_district'] = result['csv_district']
                    enhanced_excel.loc[excel_mask, 'csv_street'] = result['csv_street']
                    enhanced_excel.loc[excel_mask, 'csv_intersection'] = result['csv_intersection']
                    enhanced_excel.loc[excel_mask, 'csv_formatted_address'] = result.get('csv_formatted_address', 'N/A')
                    enhanced_excel.loc[excel_mask, 'csv_roads_detail'] = result.get('csv_roads_detail', 'N/A')

        # 保存增强后的Excel文件
        output_filename = f"enhanced_ocr表格_gemini_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        enhanced_excel.to_excel(output_filename, index=False)

        # 同时保存详细的匹配结果
        df_results.to_csv('gemini_matching_results.csv', index=False, encoding='utf-8-sig')

        # 统计信息
        matched_count = len(matched_results)
        total_count = len(results)

        print(f"\n{'='*50}")
        print(f"Gemini匹配完成 - 全部行政区")
        print(f"{'='*50}")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"增强Excel文件已保存到: {output_filename}")
        print(f"详细结果已保存到: gemini_matching_results.csv")

        # 按行政区统计
        print(f"\n按行政区统计:")
        district_stats = df_results.groupby('csv_district').agg({
            'matched': ['count', 'sum']
        }).round(2)
        district_stats.columns = ['总数', '匹配数']
        district_stats['匹配率%'] = (district_stats['匹配数'] / district_stats['总数'] * 100).round(1)
        print(district_stats)

def main():
    # 尝试从配置文件或环境变量获取API密钥
    api_key = None

    try:
        import openrouter_config
        if hasattr(openrouter_config, 'OPENROUTER_API_KEY') and openrouter_config.OPENROUTER_API_KEY != "your_openrouter_api_key_here":
            api_key = openrouter_config.OPENROUTER_API_KEY
    except ImportError:
        pass

    if not api_key:
        api_key = os.getenv('OPENROUTER_API_KEY')

    if not api_key:
        api_key = input("请输入您的OpenRouter API密钥: ").strip()

    if not api_key:
        print("错误: 需要提供OpenRouter API密钥")
        print("您可以:")
        print("1. 在openrouter_config.py中设置OPENROUTER_API_KEY")
        print("2. 设置环境变量OPENROUTER_API_KEY")
        print("3. 运行时手动输入")
        return

    matcher = OpenRouterGeminiMatcher(api_key, max_workers=2)  # 设置并发数为2，避免API限制
    matcher.load_data()

    print(f"\n开始使用Gemini Flash并发地址匹配 - 全部行政区...")
    results = matcher.match_all_districts()
    matcher.save_to_excel(results)

    # 最终保存缓存
    matcher.save_cache()

if __name__ == "__main__":
    main()
