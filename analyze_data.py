import pandas as pd
import json

# 读取Excel文件
try:
    df_excel = pd.read_excel('ocr表格1h201.9mm.xlsx')
    print('Excel文件列名:')
    print(df_excel.columns.tolist())
    print('\nExcel文件前5行:')
    print(df_excel.head())
    print(f'\nExcel文件总行数: {len(df_excel)}')
    
    # 保存为CSV以便后续处理
    df_excel.to_csv('excel_data.csv', index=False, encoding='utf-8')
    print('\n已将Excel数据保存为excel_data.csv')
    
except Exception as e:
    print(f'读取Excel文件时出错: {e}')

# 读取CSV文件
try:
    df_csv = pd.read_csv('积水点_带地址.csv', encoding='utf-8')
    print('\n\nCSV文件列名:')
    print(df_csv.columns.tolist())
    print('\nCSV文件前3行:')
    print(df_csv.head(3))
    print(f'\nCSV文件总行数: {len(df_csv)}')
    
    # 筛选未央区数据
    weiyangqu_data = df_csv[df_csv['district'] == '未央区']
    print(f'\n未央区数据行数: {len(weiyangqu_data)}')
    
    if len(weiyangqu_data) > 0:
        print('\n未央区数据示例:')
        for idx, row in weiyangqu_data.head(3).iterrows():
            print(f'ID: {row["id"]}, 地址: {row["formatted_address"]}')
            
except Exception as e:
    print(f'读取CSV文件时出错: {e}')
