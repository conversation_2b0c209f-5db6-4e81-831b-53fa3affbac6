import pandas as pd
import json
import requests
import os
import time
from datetime import datetime
from openrouter_gemini_matcher import OpenRouterGeminiMatcher

def run_full_matching():
    """运行完整的匹配程序"""
    
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取API密钥
    api_key = None
    
    try:
        import openrouter_config
        if hasattr(openrouter_config, 'OPENROUTER_API_KEY') and openrouter_config.OPENROUTER_API_KEY != "your_openrouter_api_key_here":
            api_key = openrouter_config.OPENROUTER_API_KEY
    except ImportError:
        pass
    
    if not api_key:
        api_key = os.getenv('OPENROUTER_API_KEY')
    
    if not api_key:
        print("错误: 需要提供OpenRouter API密钥")
        return
    
    # 创建匹配器
    matcher = OpenRouterGeminiMatcher(api_key, max_workers=2)  # 使用2个并发线程
    matcher.load_data()
    
    print(f"\n开始使用Gemini Flash并发地址匹配 - 全部行政区...")
    
    try:
        results = matcher.match_all_districts()
        matcher.save_to_excel(results)
        
        # 最终保存缓存
        matcher.save_cache()
        
        print(f"\n完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("匹配完成！")
        
    except KeyboardInterrupt:
        print(f"\n用户中断，保存当前进度...")
        matcher.save_cache()
        print("进度已保存，可以稍后继续运行")
        
    except Exception as e:
        print(f"\n发生错误: {e}")
        matcher.save_cache()
        print("已保存当前进度")

if __name__ == "__main__":
    run_full_matching()
