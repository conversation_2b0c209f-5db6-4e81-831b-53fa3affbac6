#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积水点坐标匹配修正脚本
功能：
1. 读取标准文件（积水点基础信息及预警指标.csv）作为参考
2. 读取待修正文件（1h201.9点位匹配.csv）
3. 通过名称和地址匹配，找出坐标差异大于1km的点位
4. 用标准文件的准确坐标更新待修正文件
5. 输出修正后的文件，并新增两列显示修正的经纬度
"""

import pandas as pd
import numpy as np
from math import radians, cos, sin, asin, sqrt
import re
from difflib import SequenceMatcher

def haversine_distance(lon1, lat1, lon2, lat2):
    """
    计算两个经纬度点之间的距离（单位：公里）
    使用Haversine公式
    """
    # 将十进制度数转化为弧度
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    
    # Haversine公式
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # 地球平均半径，单位为公里
    return c * r

def clean_text(text):
    """
    清理文本，去除特殊字符，便于匹配
    """
    if pd.isna(text):
        return ""
    text = str(text).strip()
    # 去除括号及其内容
    text = re.sub(r'[（(].*?[）)]', '', text)
    # 去除常见的方向词和连接词
    text = re.sub(r'[与和及至到向往]', '', text)
    # 去除空格和标点
    text = re.sub(r'[\s\-_、，。]', '', text)
    return text

def similarity(a, b):
    """
    计算两个字符串的相似度
    """
    return SequenceMatcher(None, a, b).ratio()

def find_best_match(target_name, target_location, standard_df, threshold=0.6):
    """
    在标准数据中找到最佳匹配
    """
    best_match = None
    best_score = 0
    
    target_name_clean = clean_text(target_name)
    target_location_clean = clean_text(target_location)
    
    for idx, row in standard_df.iterrows():
        if pd.isna(row['经度']) or pd.isna(row['纬度']):
            continue
            
        std_name_clean = clean_text(row['名称'])
        std_location_clean = clean_text(row['具体位置'])
        
        # 计算名称相似度
        name_sim = similarity(target_name_clean, std_name_clean)
        
        # 计算位置相似度
        location_sim = similarity(target_location_clean, std_location_clean)
        
        # 综合相似度（名称权重更高）
        total_score = name_sim * 0.7 + location_sim * 0.3
        
        if total_score > best_score and total_score >= threshold:
            best_score = total_score
            best_match = {
                'index': idx,
                'name': row['名称'],
                'location': row['具体位置'],
                'longitude': row['经度'],
                'latitude': row['纬度'],
                'score': total_score
            }
    
    return best_match

def main():
    print("开始处理积水点坐标匹配修正...")
    
    # 读取标准文件
    print("读取标准文件：积水点基础信息及预警指标.csv")
    try:
        standard_df = pd.read_csv('积水点基础信息及预警指标.csv', encoding='utf-8')
    except UnicodeDecodeError:
        standard_df = pd.read_csv('积水点基础信息及预警指标.csv', encoding='gbk')
    
    # 读取待修正文件
    print("读取待修正文件：1h201.9点位匹配.csv")
    try:
        target_df = pd.read_csv('1h201.9点位匹配.csv', encoding='utf-8')
    except UnicodeDecodeError:
        target_df = pd.read_csv('1h201.9点位匹配.csv', encoding='gbk')
    
    print(f"标准文件包含 {len(standard_df)} 条记录")
    print(f"待修正文件包含 {len(target_df)} 条记录")
    
    # 新增列用于存储修正后的坐标
    target_df['修正后经度'] = target_df['经度'].copy()
    target_df['修正后纬度'] = target_df['纬度'].copy()
    target_df['坐标修正说明'] = ''
    
    modified_count = 0
    matched_count = 0
    
    print("\n开始匹配和修正坐标...")

    for idx, row in target_df.iterrows():
        # 在标准文件中查找匹配（不管原始坐标是否为空都进行匹配）
        best_match = find_best_match(row['风险点'], row['风险点'], standard_df)

        if best_match:
            matched_count += 1
            standard_lon = float(best_match['longitude'])
            standard_lat = float(best_match['latitude'])

            print(f"第{idx+1}行：{row['风险点']}")
            print(f"  匹配到：{best_match['name']} (相似度: {best_match['score']:.2f})")
            print(f"  标准坐标：({standard_lon:.6f}, {standard_lat:.6f})")

            # 检查原始坐标是否存在
            if pd.isna(row['经度']) or pd.isna(row['纬度']):
                # 原始坐标为空，直接补充标准坐标
                target_df.at[idx, '修正后经度'] = standard_lon
                target_df.at[idx, '修正后纬度'] = standard_lat
                target_df.at[idx, '坐标修正说明'] = '原始坐标为空，已补充标准坐标'
                modified_count += 1
                print(f"  ✓ 原始坐标为空，已补充标准坐标")
            else:
                # 原始坐标存在，计算距离差异
                original_lon = float(row['经度'])
                original_lat = float(row['纬度'])
                distance = haversine_distance(original_lon, original_lat, standard_lon, standard_lat)

                print(f"  原坐标：({original_lon:.6f}, {original_lat:.6f})")
                print(f"  距离差异：{distance:.2f} km")

                # 如果距离大于1km，则更新坐标
                if distance > 1.0:
                    target_df.at[idx, '修正后经度'] = standard_lon
                    target_df.at[idx, '修正后纬度'] = standard_lat
                    target_df.at[idx, '坐标修正说明'] = f'原坐标距离差异{distance:.2f}km，已更新为标准坐标'
                    modified_count += 1
                    print(f"  ✓ 坐标已修正")
                else:
                    target_df.at[idx, '坐标修正说明'] = f'坐标准确，距离差异仅{distance:.2f}km'
                    print(f"  ○ 坐标准确，无需修正")
            print()
        else:
            print(f"第{idx+1}行：{row['风险点']} - 未找到匹配的标准点位")
            target_df.at[idx, '坐标修正说明'] = '未找到匹配的标准点位'
            print()
    
    # 保存修正后的文件
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f'1h201.9点位匹配_坐标修正版_{timestamp}.csv'
    target_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
    
    print(f"\n处理完成！")
    print(f"总计处理：{len(target_df)} 条记录")
    print(f"成功匹配：{matched_count} 条记录")
    print(f"坐标修正：{modified_count} 条记录")
    print(f"输出文件：{output_filename}")
    
    # 输出修正统计
    if modified_count > 0:
        print(f"\n修正的点位列表：")
        # 包括坐标修正和坐标补充的情况
        modified_rows = target_df[target_df['坐标修正说明'].str.contains('已更新|已补充', na=False)]
        for idx, row in modified_rows.iterrows():
            print(f"- {row['风险点']}: {row['坐标修正说明']}")

    # 输出补充坐标的统计
    supplemented_rows = target_df[target_df['坐标修正说明'].str.contains('已补充', na=False)]
    supplemented_count = len(supplemented_rows)
    if supplemented_count > 0:
        print(f"\n补充坐标的点位列表：")
        for idx, row in supplemented_rows.iterrows():
            print(f"- {row['风险点']}: 原始坐标为空，已补充标准坐标 ({row['修正后经度']:.6f}, {row['修正后纬度']:.6f})")

if __name__ == "__main__":
    main()
