import pandas as pd
import json
import requests
import time
import asyncio
import aiohttp
from typing import List, Dict, Any
import config
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import pickle
from datetime import datetime

class ConcurrentCompleteMatcher:
    def __init__(self, max_workers=5):
        self.api_key = config.ZHIPU_API_KEY
        self.base_url = config.BASE_URL
        self.model = config.MODEL_NAME
        self.max_workers = max_workers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.cache_file = "matching_cache.pkl"
        self.results_cache = self.load_cache()
    
    def load_cache(self):
        """加载缓存数据"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                print(f"已加载缓存数据: {len(cache)} 条记录")
                return cache
            except Exception as e:
                print(f"加载缓存失败: {e}")
                return {}
        return {}
    
    def save_cache(self):
        """保存缓存数据"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.results_cache, f)
            print(f"已保存缓存数据: {len(self.results_cache)} 条记录")
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载CSV数据（积水点提取字段）
        self.csv_data = pd.read_csv('积水点_提取字段1.csv', encoding='gbk')
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel(config.EXCEL_FILE)
        
        print(f"CSV总数据: {len(self.csv_data)} 条")
        print(f"Excel总数据: {len(self.excel_data)} 条")
        
        # 显示各行政区的数据分布
        print(f"\nCSV数据行政区分布:")
        csv_districts = self.csv_data['district'].value_counts()
        for district, count in csv_districts.items():
            print(f"  {district}: {count} 条")
        
        print(f"\nExcel数据行政区分布:")
        excel_districts = self.excel_data['行政区'].value_counts()
        for district, count in excel_districts.items():
            print(f"  {district}: {count} 条")
    
    def call_zhipu_api(self, prompt: str) -> str:
        """调用智谱AI API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=config.TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建匹配提示词"""
        
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 行政区: {csv_record['district']}
- 街道: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 方向: {csv_record['street_number_direction']}
- 路口详情: {csv_record['roadinters_detail']}

Excel标准风险点列表（{csv_record['district']}）：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请逐一对比CSV积水点的各个字段与Excel风险点的匹配情况：

匹配分析要求：
1. **街道名称匹配**: 对比CSV的street_number_street字段与Excel风险点中的道路名称
2. **路口匹配**: 分析CSV的roadinters_detail字段与Excel风险点中的交叉口描述
3. **关键词匹配**: 注意"隧道"、"下穿"、"立交"、"桥"等关键词
4. **地理位置**: 考虑街道(township)信息辅助判断
5. **精确匹配优先**: 优先选择字段内容直接匹配的记录

匹配策略：
- 如果CSV的street_number_street与Excel风险点中的道路名完全匹配，高优先级
- 如果CSV的roadinters_detail中的路口信息与Excel风险点描述匹配，高优先级
- 注意Excel风险点中的特殊描述（如"隧道"、"下穿"等）
- 避免模糊匹配，确保匹配的准确性

请严格按照以下JSON格式返回结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程",
    "matched_elements": ["匹配的关键要素列表"]
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        csv_id = csv_record['id']
        
        # 检查缓存
        if csv_id in self.results_cache:
            print(f"从缓存获取 CSV ID {csv_id}")
            return self.results_cache[csv_id]
        
        print(f"处理CSV记录 ID {csv_id} ({csv_record['district']})")
        
        # 创建匹配提示词
        prompt = self.create_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_zhipu_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            
            # 添加CSV记录信息
            result.update({
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record['roadinters_detail'],
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat']
            })
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']}")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            result = {
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record['roadinters_detail'],
                'longitude': csv_record['lon'],
                'latitude': csv_record['lat'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_elements': []
            }
        
        # 保存到缓存
        self.results_cache[csv_id] = result
        return result
    
    def match_district_concurrent(self, district: str):
        """并发匹配单个行政区的数据"""
        print(f"\n{'='*50}")
        print(f"开始并发处理 {district}")
        print(f"{'='*50}")
        
        # 筛选当前行政区的数据
        district_csv = self.csv_data[self.csv_data['district'] == district]
        district_excel = self.excel_data[self.excel_data['行政区'] == district]
        
        print(f"{district} CSV数据: {len(district_csv)} 条")
        print(f"{district} Excel数据: {len(district_excel)} 条")
        
        if len(district_excel) == 0:
            print(f"警告: {district} 没有Excel标准数据，跳过")
            return []
        
        excel_records = district_excel.to_dict('records')
        csv_records = district_csv.to_dict('records')
        
        # 使用线程池并发处理
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_record = {
                executor.submit(self.match_single_record, csv_record, excel_records): csv_record
                for csv_record in csv_records
            }
            
            # 收集结果
            for future in as_completed(future_to_record):
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 每处理10条记录保存一次缓存
                    if len(results) % 10 == 0:
                        self.save_cache()
                        print(f"已处理 {len(results)}/{len(csv_records)} 条记录")
                        
                except Exception as e:
                    csv_record = future_to_record[future]
                    print(f"处理CSV记录 {csv_record['id']} 时出错: {e}")
        
        return results
    
    def match_all_districts(self):
        """匹配所有行政区的数据"""
        all_results = []
        
        # 获取所有行政区
        districts = self.csv_data['district'].unique()
        
        for district in districts:
            district_results = self.match_district_concurrent(district)
            all_results.extend(district_results)
            
            # 每个行政区处理完后保存缓存
            self.save_cache()
            print(f"{district} 处理完成，累计结果: {len(all_results)} 条")
        
        return all_results
    
    def save_to_excel(self, results: List[Dict]):
        """将结果保存到Excel文件"""
        print(f"\n开始保存结果到Excel文件...")
        
        # 创建结果DataFrame
        df_results = pd.DataFrame(results)
        
        # 读取原始Excel文件
        original_excel = pd.read_excel(config.EXCEL_FILE)
        
        # 为每个Excel记录添加匹配信息
        enhanced_excel = original_excel.copy()
        
        # 初始化新列
        new_columns = ['经度', '纬度', 'csv_id', '置信度', 'reason', 'matched_elements', 
                      'csv_district', 'csv_street', 'csv_intersection']
        for col in new_columns:
            enhanced_excel[col] = None
        
        # 填充匹配成功的记录
        matched_results = df_results[df_results['matched'] == True]
        
        for _, result in matched_results.iterrows():
            excel_code = result['excel_code']
            if excel_code:
                # 找到对应的Excel记录
                excel_mask = enhanced_excel['编号'] == excel_code
                if excel_mask.any():
                    enhanced_excel.loc[excel_mask, '经度'] = result['longitude']
                    enhanced_excel.loc[excel_mask, '纬度'] = result['latitude']
                    enhanced_excel.loc[excel_mask, 'csv_id'] = result['csv_id']
                    enhanced_excel.loc[excel_mask, '置信度'] = result['confidence']
                    enhanced_excel.loc[excel_mask, 'reason'] = result['reason']
                    enhanced_excel.loc[excel_mask, 'matched_elements'] = str(result['matched_elements'])
                    enhanced_excel.loc[excel_mask, 'csv_district'] = result['csv_district']
                    enhanced_excel.loc[excel_mask, 'csv_street'] = result['csv_street']
                    enhanced_excel.loc[excel_mask, 'csv_intersection'] = result['csv_intersection']
        
        # 保存增强后的Excel文件
        output_filename = f"enhanced_ocr表格_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        enhanced_excel.to_excel(output_filename, index=False)
        
        # 同时保存详细的匹配结果
        df_results.to_csv('complete_matching_results.csv', index=False, encoding='utf-8-sig')
        
        # 统计信息
        matched_count = len(matched_results)
        total_count = len(results)
        
        print(f"\n{'='*50}")
        print(f"匹配完成 - 全部行政区")
        print(f"{'='*50}")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"增强Excel文件已保存到: {output_filename}")
        print(f"详细结果已保存到: complete_matching_results.csv")
        
        # 按行政区统计
        print(f"\n按行政区统计:")
        district_stats = df_results.groupby('csv_district').agg({
            'matched': ['count', 'sum']
        }).round(2)
        district_stats.columns = ['总数', '匹配数']
        district_stats['匹配率%'] = (district_stats['匹配数'] / district_stats['总数'] * 100).round(1)
        print(district_stats)

def main():
    matcher = ConcurrentCompleteMatcher(max_workers=3)  # 设置并发数
    matcher.load_data()
    
    print(f"\n开始并发地址匹配 - 全部行政区...")
    results = matcher.match_all_districts()
    matcher.save_to_excel(results)
    
    # 最终保存缓存
    matcher.save_cache()

if __name__ == "__main__":
    main()
