# OpenRouter API配置
# 请在这里填入您的OpenRouter API密钥
# 您可以在 https://openrouter.ai/ 获取API密钥
OPENROUTER_API_KEY = "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"

# API配置
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL_NAME = "google/gemini-2.5-flash"

# 请求配置
MAX_TOKENS = 2000
TEMPERATURE = 0.5
TIMEOUT = 60

# 并发配置
MAX_WORKERS = 2  # 并发线程数，建议不要太高以避免API限制
DELAY_BETWEEN_REQUESTS = 0.5  # 请求间隔（秒）

# 缓存配置
CACHE_SAVE_INTERVAL = 5  # 每处理多少条记录保存一次缓存

# 文件配置
CSV_FILE = "积水点_提取字段.csv"
EXCEL_FILE = "ocr表格1h201.9mm.xlsx"
