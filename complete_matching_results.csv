﻿matched,excel_index,excel_code,confidence,reason,matched_elements,csv_id,csv_district,csv_street,csv_intersection,longitude,latitude
True,41.0,XA69,1.0,CSV中的主要街道'凤城九路'与Excel风险点列表中的'凤城九路'完全匹配，且CSV中的行政区'未央区'与Excel风险点列表中的'未央区'一致，因此可以确定匹配。,"['主要街道', '行政区']",2,未央区,凤城九路,"路口:明光路与凤城九路,方向:东,距离:294.735m",108.928245,34.348988
True,9.0,XA58,0.9,"CSV中的主要街道'文景路'与Excel风险点中的'文景路立交'完全匹配，且CSV中的路口详情'路口:文景路与凤城九路,方向:东北,距离:44.4347m'与Excel风险点中的'文景路立交'描述相符，因此匹配度较高。","['主要街道', '路口详情']",1,未央区,文景路,"路口:文景路与凤城九路,方向:东北,距离:44.4347m",108.933768,34.349163
True,41.0,XA69,1.0,CSV中的主要街道'凤城九路'与Excel风险点列表中的'凤城九路'完全匹配，且CSV中的路口详情'路口:凤城九路与朱宏路辅路'与Excel风险点列表中的'凤城九路'也匹配，因此可以确定匹配。,"['主要街道', '路口详情']",3,未央区,凤城九路,"路口:凤城九路与朱宏路辅路,方向:北,距离:46.0937m",108.914918,34.349339
True,61.0,XA164,0.95,CSV中的积水点位于未央区汉城街道凤城十二路，与Excel中编号为XA164的风险点描述'文景路与凤城12路交汇'高度匹配，且地理位置和街道名称均一致。,"['未央区', '汉城街道', '凤城十二路', '文景路与凤城12路交汇']",5,未央区,凤城十二路,"路口:凤城十二路与文景路,方向:东北,距离:22.135m",108.934207,34.360035
True,24.0,XA54,1.0,"CSV中的主要街道'朱宏路'与Excel风险点列表中的'朱宏路凤城七路'完全匹配，且CSV中的路口详情'路口:朱宏路辅路与S1机场专用高速辅路,方向:西南,距离:335.875m'与Excel风险点描述中的'凤城七路'相符，因此可以确定匹配。","['朱宏路', '凤城七路', '路口:朱宏路辅路与S1机场专用高速辅路']",4,未央区,朱宏路,"路口:朱宏路辅路与S1机场专用高速辅路,方向:西南,距离:335.875m",108.913954,34.35872
True,23.0,XA236,0.9,CSV中的主要街道'凤城十二路'与Excel风险点中的'凤城十二路'完全匹配，且CSV中的路口详情'S1机场专用高速出口与朱宏路'与Excel风险点中的'M明光路渭河南地铁站'在地理位置上相近，考虑到'下穿'关键词，可以认为匹配。,"['凤城十二路', 'S1机场专用高速出口与朱宏路', '下穿']",6,未央区,凤城十二路,"路口:S1机场专用高速出口与朱宏路,方向:东南,距离:318.715m",108.918601,34.364068
True,39.0,XA76,1.0,CSV中的积水点位于明光路，且路口详情明确指出是明光路与北三环辅路的交叉口，这与Excel中编号为XA76的风险点描述'明光路下穿'完全匹配。,"['明光路', '北三环辅路', '下穿']",7,未央区,明光路,"路口:明光路与北三环辅路,方向:北,距离:68.2148m",108.925352,34.367137
True,23.0,XA236,0.9,CSV中的积水点位于未央区汉城街道凤城北路，与Excel中的风险点编号XA236描述的'明光路渭河南地铁站'地理位置相近，且CSV中的路口详情提到凤城北路与元光路交汇，这与Excel风险点描述的明光路相关，因此匹配度较高。,"['未央区', '凤城北路', '明光路']",8,未央区,凤城北路,"路口:凤城北路与元光路,方向:北,距离:77.6108m",108.939117,34.365997
True,4.0,XA163,0.9,CSV中的积水点位于建元路，与Excel风险点中的建元路与元凤一路交汇（桥下）完全匹配，且CSV中的路口详情提到了建元路，因此匹配度较高。,"['建元路', '桥下', '路口:元鼎路与建元路']",9,未央区,建元路,"路口:元鼎路与建元路,方向:东南,距离:73.1565m",108.943676,34.369065
True,11.0,XA71,1.0,"CSV中的主要街道'元朔路'与Excel风险点列表中的'元朔路下穿'完全匹配，且CSV中的路口详情'路口:文景路与元朔大道辅路,方向:西,距离:254.262m'与风险点描述中的'下穿'关键词相符，表明该积水点位于元朔路下穿区域。","['元朔路', '下穿']",10,未央区,元朔路,"路口:文景路与元朔大道辅路,方向:西,距离:254.262m",108.931839,34.372923
True,19.0,XA68,0.95,CSV中的积水点位于未央区汉城街道尚宏路，与Excel标准风险点列表中的编号为XA68的风险点'北辰永淳路隧道'匹配，尽管CSV中未提及'隧道'，但尚宏路高铁桥下与尚宏路相符，且地理位置上属于未央区，因此匹配度较高。,"['未央区', '汉城街道', '尚宏路', '高铁桥下']",11,未央区,尚宏路,"路口:朱宏路辅路与尚耕路,方向:东北,距离:154.145m",108.914568,34.372222
True,20.0,XA54,1.0,"CSV中的主要街道'朱宏路'与Excel风险点列表中的'朱宏路凤城七路'完全匹配，且CSV中的路口详情'路口:崇信路与尚耕路,方向:北,距离:191.342m'与Excel风险点列表中的'朱宏路凤城七路'描述相符，因此可以确定匹配。","['主要街道', '路口详情']",12,未央区,朱宏路,"路口:崇信路与尚耕路,方向:北,距离:191.342m",108.898172,34.364506
True,24.0,XA54,1.0,CSV中的主要街道'朱宏路'与Excel风险点列表中的'朱宏路凤城七路'完全匹配，且CSV中的行政区'未央区'与Excel风险点列表中的'未央区'一致，因此可以确定匹配。,"['朱宏路', '未央区']",13,未央区,朱宏路,"路口:回望路与崇德路,方向:东,距离:263.375m",108.905274,34.361613
True,20.0,XA54,1.0,CSV中的主要街道'朱宏路'与Excel风险点列表中的'朱宏路凤城七路'完全匹配，且CSV中的路口详情'路口:朱宏路辅路与凤城七路'与Excel风险点描述中的'朱宏路凤城七路'相符，因此可以确定匹配。,"['主要街道', '路口详情']",14,未央区,朱宏路,"路口:朱宏路辅路与凤城七路,方向:北,距离:24.2306m",108.915094,34.338818
True,5.0,XA243,0.9,CSV中的积水点位于未央区六村堡街道元凤二路，与Excel标准风险点列表中的编号为XA243的风险点匹配，该风险点位于明光路与北站西路交汇以南（下穿），虽然CSV中的积水点不是在北站西路，但考虑到明光路和北站西路的交汇处附近，且CSV中提到的路口详情与风险点描述的'下穿'关键词相符合，因此判断为匹配。,"['未央区', '六村堡街道', '元凤二路', '明光路', '下穿']",15,未央区,元凤二路,"路口:明光路与明光路辅路,方向:北,距离:300.454m",108.924826,34.376693
True,85.0,XA211,0.95,"CSV中的经纬度(108.96086, 34.331365)与Excel中编号为XA211的风险点凤城五路与渭槟路十字地理位置相近，且CSV中的路口详情与Excel风险点描述中的凤城五路与渭槟路十字完全匹配。","['经纬度', '路口详情']",16,未央区,太华北路,"路口:凤城五路与渭滨街南段,方向:东,距离:18.4151m",108.96086,34.331365
True,28.0,XA28,1.0,CSV中的积水点位于未央区张家堡街道凤城三路，与Excel标准风险点列表中的编号为XA28的风险点（开元路与凤城三路十字）完全匹配。,"['未央区', '张家堡街道', '凤城三路', '开元路与凤城三路十字']",17,未央区,凤城三路,"路口:开元路与凤城三路,方向:南,距离:42.1983m",108.949199,34.323211
True,38.0,XA165,1.0,CSV中的积水点位于二环北路东段辅路与贞观路南段交汇处，与Excel中编号为XA165的风险点描述'二环北路与贞观路南段交汇'完全匹配，且关键词'二环北路'和'贞观路'均出现在CSV和Excel的描述中。,"['二环北路', '贞观路', '交汇']",18,未央区,二环北路东段,"路口:二环北路东段辅路与贞观路南段,方向:南,距离:13.9697m",108.955775,34.3112
True,14.0,XA57,0.9,CSV中的积水点位于未央区未央路，且路口详情提到二环北路东段辅路与未央路辅路交汇，这与Excel中编号为XA57的风险点'未央路立交'高度匹配。虽然CSV中的积水点没有明确提到'立交'，但考虑到未央路和二环北路交汇的地理位置，可以合理推断该积水点与未央路立交相关。,"['未央区', '未央路', '二环北路东段辅路', '未央路辅路', '立交']",19,未央区,未央路,"路口:二环北路东段辅路与未央路辅路,方向:西,距离:104.209m",108.942624,34.311112
False,,,0.0,经过对比，CSV中的积水点位于太华北路永庆路与凤城一路交叉口西南方向，距离65.218米。然而，在Excel标准风险点列表中，没有找到完全匹配的街道名称、路口描述或包含的关键词。虽然太华北路在列表中多次出现，但具体的交叉口和方向描述没有匹配项。,[],20,未央区,太华北路,"路口:永庆路与凤城一路,方向:南,距离:65.218m",108.971381,34.314795
False,,,0.0,经过对比，CSV中的积水点位于未央区大明宫街道二环北路东段，但与Excel标准风险点列表中的任何风险点都没有完全匹配的街道名称、路口信息或关键词。虽然Excel中有一个风险点位于二环北路西段辅路，但CSV中的积水点位于东段，因此无法匹配。,[],21,未央区,二环北路东段,"路口:永华路与永昌路,方向:西南,距离:142.336m",108.968488,34.311814
True,38.0,XA38,0.9,CSV中的积水点位于二环北路东段，与Excel中编号为XA38的风险点描述'二环北路与贞观路南段交汇'高度匹配，且CSV中的路口详情提到了二环北路，因此可以认为这是一个高置信度的匹配。,"['二环北路', '东段', '贞观路', '交汇']",22,未央区,二环北路东段,"路口:二环北路东段入口与行舍路,方向:东南,距离:67.1969m",108.975326,34.311288
False,,,0.0,经过对比分析，CSV中的积水点位于太华北路，但Excel标准风险点列表中并没有直接提及太华北路。虽然CSV中的路口详情提到了太华南路与玄武路的交叉口，但Excel风险点列表中并没有提及这个交叉口。因此，无法找到完全匹配的记录。,[],23,未央区,太华北路,"路口:太华南路与玄武路,方向:东南,距离:53.9298m",108.966909,34.306904
True,29.0,XA159,1.0,"CSV中的街道名称'太元路'与Excel风险点中的'太元路与太和路'完全匹配，且路口详情'路口:太元路与太和路,方向:东,距离:26.075m'与Excel风险点中的'太元路与太和路十字'匹配，同时CSV中的'行政区: 未央区'与Excel风险点所在的区域一致。","['街道名称', '路口详情', '行政区']",24,未央区,太元路,"路口:太元路与太和路,方向:东,距离:26.075m",108.979885,34.302695
True,25.0,XA175,1.0,CSV中的积水点位于北辰东路，与Excel标准风险点列表中编号为XA175的风险点描述完全一致，该风险点即为北辰东路。,"['行政区：未央区', '街道：辛家庙街道', '主要街道：北辰东路']",27,未央区,玄武东路,"路口:北辰东路与玄武东路,方向:东南,距离:47.6255m",108.998648,34.305939
True,51.0,XA51,1.0,CSV中的积水点位于浐灞大道与广运潭西路交汇处，与Excel中编号为XA51的风险点描述'浐灞大道与广运潭西路交汇'完全匹配，且行政区为未央区，因此可以确定匹配。,"['浐灞大道与广运潭西路交汇', '未央区']",26,未央区,广运潭西路,"路口:浐灞大道与广运潭西路,方向:东南,距离:36.3422m",109.004785,34.315321
True,10.0,XA61,1.0,CSV中的积水点位于永城路，且路口详情中提到永城路辅路与行舍路交汇，这与Excel中编号为XA61的风险点'永城路下穿'完全匹配。,"['永城路', '永城路辅路与行舍路交汇']",28,未央区,永城路,"路口:永城路辅路与行舍路,方向:东,距离:124.774m",108.977255,34.330751
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为永信路，而Excel风险点列表中没有直接提及永信路。虽然CSV中的路口详情提到了永淳路与永淳路辅路，但在Excel风险点列表中也没有找到与之完全对应的描述。因此，无法确定积水点与风险点的匹配关系。,[],30,未央区,永信路,"路口:永淳路与永淳路辅路,方向:东,距离:290.275m",108.976115,34.341448
True,10.0,XA61,0.9,CSV中的积水点位于北辰路，且路口详情提到永城路与欧亚大道辅路交汇，这与Excel中编号为XA61的风险点'永城路下穿'完全匹配。虽然CSV中的积水点位于永城路西方向，而Excel风险点没有明确方向，但考虑到永城路下穿是一个具体的地点，因此匹配度较高。,"['北辰路', '永城路', '下穿']",29,未央区,北辰路,"路口:永城路与欧亚大道辅路,方向:东,距离:89.478m",108.991458,34.330401
True,7.0,XA62,0.95,CSV中的积水点位于北辰永淳路，与Excel中的风险点编号XA62的北辰永淳路隧道完全匹配。虽然CSV中没有提到'隧道'这个词，但根据街道名称和位置信息，可以合理推断两者是同一地点。,"['北辰永淳路', '隧道']",31,未央区,北辰路,"路口:永淳路与锦堤六路辅路,方向:东北,距离:51.1806m",108.99102,34.343903
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为北辰路，但Excel列表中没有直接提及北辰路。虽然CSV中提到了渭青路与北辰路辅路的交叉口，但Excel列表中没有对应的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],32,未央区,北辰路,"路口:渭青路与北辰路辅路,方向:东南,距离:310.687m",108.991634,34.348637
True,22.0,XA140,1.0,CSV中的积水点位于未央区的张家堡街道，主要街道为二环北路西段，这与Excel中编号为XA140的风险点描述'二环北路西段辅路'完全匹配。,"['未央区', '张家堡街道', '二环北路西段']",34,未央区,二环北路西段,"路口:文景路与凤鸣路,方向:南,距离:183.19m",108.933681,34.311638
True,21.0,XA174,0.95,CSV中的主要街道'鱼藻路'与Excel风险点中的'贞观路与开成路十字'存在部分匹配，且CSV中的路口详情'路口:开成路与贞观路'与Excel风险点描述完全一致，同时CSV中的'行政区:未央区'与Excel风险点列表中的区域相匹配，因此判断为匹配。,"['行政区:未央区', '主要街道:鱼藻路', '路口详情:路口:开成路与贞观路']",33,未央区,鱼藻路,"路口:开成路与贞观路,方向:北,距离:26.6656m",108.955687,34.345481
True,22.0,XA140,1.0,"CSV中的主要街道'二环北路西段'与Excel风险点列表中的'二环北路西段辅路'完全匹配，且CSV中的路口详情'路口:明光路辅路与二环北路西段辅路,方向:东北,距离:91.1517m'与Excel风险点'二环北路西段辅路'的描述相符，因此可以确定匹配。","['二环北路西段', '明光路辅路与二环北路西段辅路']",35,未央区,二环北路西段,"路口:明光路辅路与二环北路西段辅路,方向:东北,距离:91.1517m",108.925702,34.311638
True,23.0,XA236,0.9,CSV中的积水点位于未央区汉城街道凤城二路，与Excel中编号为XA236的风险点描述'明光路渭河南地铁站'存在地理位置上的相关性，且CSV中的路口详情提到了朱宏路，与Excel风险点中的'明光路渭河南地铁站'存在交叉，因此具有较高的匹配度。,"['未央区', '汉城街道', '凤城二路', '朱宏路', '明光路渭河南地铁站']",36,未央区,凤城二路,"路口:朱宏路辅路与凤城二路,方向:东,距离:234.587m",108.920354,34.319529
True,24.0,XA54,0.9,"CSV中的主要街道'朱宏路'与Excel风险点列表中的'朱宏路凤城七路'完全匹配，且CSV中的路口详情'路口:朱宏路辅路与朱宏路,方向:西,距离:260.176m'与Excel风险点描述中的'凤城七路'相符，表明积水点位于该风险点附近。","['朱宏路', '凤城七路', '路口:朱宏路辅路与朱宏路']",37,未央区,朱宏路,"路口:朱宏路辅路与朱宏路,方向:西,距离:260.176m",108.916058,34.306378
True,39.0,XA76,1.0,CSV中的积水点位于明光路，且路口详情明确指出是明光路与纬二十九街的交叉口，这与Excel中编号为XA76的风险点描述'明光路下穿'完全匹配。,"['明光路', '下穿', '纬二十九街']",38,未央区,明光路,"路口:明光路与纬二十九街,方向:东南,距离:48.4159m",108.927368,34.303397
True,47.0,XA255,1.0,CSV中的积水点位于凤城南路，与Excel风险点中的凤城南路与文景路十字完全匹配，且路口详情中提到的凤城南路与纬二十九街交叉口方向和距离与凤城南路与文景路十字相符。,"['街道名称：凤城南路', '路口详情：凤城南路与纬二十九街交叉口']",39,未央区,凤城南路,"路口:凤城南路与纬二十九街,方向:东南,距离:59.6316m",108.93482,34.303485
False,,,0.0,CSV中的积水点位于红旗东路与太华北路的交叉口，但Excel标准风险点列表中并没有直接匹配的道路名称或交叉口描述。虽然存在'下穿'和'立交'等关键词，但它们并不直接对应CSV中的具体位置。,[],122,未央区,红旗东路,"路口:红旗东路与太华北路,方向:西北,距离:108.541m",108.975589,34.355958
True,15.0,XA75,0.9,CSV中的积水点位于北辰路辅路与元朔大道交汇处，且方向为东，距离为67.0186米。这与Excel中编号为XA75的风险点描述'西铜路秦汉大道立交'相匹配，尽管CSV中没有明确提到'立交'，但元朔大道与秦汉大道的交汇处可以合理推断为立交，因此匹配度较高。,"['北辰路', '元朔大道', '交汇', '立交']",121,未央区,北辰路,"路口:北辰路辅路与元朔大道,方向:东,距离:67.0186m",108.991371,34.373318
True,40.0,XA216,1.0,CSV中的积水点位于启源三路，与Excel风险点列表中的编号为XA216的风险点完全匹配，该风险点描述为启源三路，且风险等级为红色。,['启源三路'],136,未央区,启源二路,"路口:启源三路与灞河西路,方向:西北,距离:154.752m",109.003119,34.412508
True,74.0,XA207,0.95,CSV中的积水点位于龙朔路与元朔大道交汇处，这与Excel中编号为XA207的风险点描述'龙朔路与元朔大道以南'高度匹配，且CSV中的行政区、街道、主要街道、方向和路口详情均与该风险点所在位置相符。,"['龙朔路', '元朔大道', '交汇处', '以南']",124,未央区,北三环,"路口:武德路与元朔大道,方向:东北,距离:34.8457m",108.976904,34.375334
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'武德路'，而Excel列表中没有直接匹配的街道名称。路口详情为'东环与北环，方向：东，距离：196.329m'，但Excel列表中没有包含此类具体的路口描述。尽管存在'下穿'等关键词，但它们并不足以与Excel列表中的任何风险点精确匹配。,[],123,未央区,武德路,"路口:东环与北环,方向:东,距离:196.329m",108.977693,34.380332
True,74.0,XA207,1.0,CSV中的积水点位于龙朔路与元朔大道以南，这与Excel中编号为XA207的风险点描述完全一致，且行政区、街道、主要街道等信息也相匹配。,"['龙朔路', '元朔大道', '以南']",139,未央区,龙朔路,"路口:龙朔路与元朔大道,方向:西北,距离:72.9907m",108.96086,34.377263
True,37.0,XA162,1.0,CSV中的积水点位于兴华路，与Excel风险点列表中编号为XA162的风险点描述'兴华路'完全匹配，且没有其他更精确的匹配项。,"['街道名称：兴华路', '路口详情：兴华路与大桥南路辅路']",138,未央区,东兴路,"路口:兴华路与大桥南路辅路,方向:南,距离:236.882m",108.959282,34.387083
True,15.0,XA75,1.0,CSV中的积水点位于未央区草滩街道东风路，路口详情为秦汉大道与西铜路交汇处，方向为西北，距离368.438米。这与Excel标准风险点列表中编号为XA75的风险点描述'西铜路秦汉大道立交'完全匹配，因此可以确定积水点与风险点匹配。,"['未央区', '草滩街道', '东风路', '秦汉大道与西铜路交汇', '西北方向']",137,未央区,东风路,"路口:秦汉大道与西铜路,方向:西北,距离:368.438m",108.966997,34.39357
False,,,0.0,经过对比，CSV中的积水点位于龙朔路，但Excel标准风险点列表中并没有直接匹配的道路名称。虽然CSV中的路口详情提到了昭远门路与昭远门路辅路，但Excel风险点列表中没有对应的交叉口描述。因此，无法找到完全匹配的记录。,[],141,未央区,龙朔路,"路口:昭远门路与昭远门路辅路,方向:东,距离:254.78m",108.960685,34.371827
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'显庆路'，而Excel列表中没有直接匹配的街道名称。虽然CSV中提到了'昭远门路与显庆路'的路口，但在Excel列表中没有找到包含这两个名称的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],140,未央区,显庆路,"路口:昭远门路与显庆路,方向:北,距离:97.8194m",108.966296,34.372178
False,98.0,XA98,1.0,CSV中的积水点位于北三环与龙朔南路交汇处，与Excel中编号为XA98的风险点描述'北三环路与龙朔南路交汇（桥下）'完全匹配。,"['北三环路', '龙朔南路', '交汇', '桥下']",142,未央区,北三环,"路口:龙朔南路与北三环,方向:东北,距离:428.159m",108.957879,34.366917
True,11.0,XA71,1.0,CSV中的积水点位于元朔路，与Excel标准风险点列表中的编号为XA71的风险点'元朔路下穿'完全匹配，且行政区为未央区，因此匹配成功。,"['元朔路', '下穿', '未央区']",144,未央区,元朔路,"路口:建元三路与元朔大道,方向:东北,距离:402.032m",108.956651,34.378666
True,15.0,XA75,0.9,CSV中的积水点位于西铜路与元鼎路交叉口，距离311.805米，与Excel中编号为XA75的风险点'西铜路秦汉大道立交'匹配度较高，尽管CSV中的积水点不在立交处，但考虑到地理位置的邻近性以及交叉口的描述，可以认为这是一个高置信度的匹配。,"['西铜路', '元鼎路', '交叉口', '秦汉大道立交']",143,未央区,建元一路南段,"路口:西铜路与元鼎路,方向:东南,距离:311.805m",108.952268,34.367794
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的主要街道为东兴路，而Excel风险点列表中未包含东兴路。路口详情为秦汉大道与建元路辅路，但Excel风险点列表中未包含此交叉口的具体描述。因此，无法找到匹配的风险点。,[],145,未央区,东兴路,"路口:秦汉大道与建元路辅路,方向:北,距离:210.488m",108.942097,34.386381
False,,,0.0,经过对比，CSV中的积水点位于尚贤路，而Excel标准风险点列表中并没有直接提及尚贤路。虽然CSV中的路口详情提到了尚华路与尚稷路，但在Excel风险点列表中也没有找到与之完全匹配的路口描述。因此，无法确定CSV中的积水点与Excel标准风险点列表中的某个风险点匹配。,[],147,未央区,尚贤路,"路口:尚华路与尚稷路,方向:东南,距离:359.1m",108.944552,34.392869
True,11.0,XA71,1.0,"CSV中的主要街道'元朔路'与Excel风险点列表中的'元朔路下穿'完全匹配，且CSV中的路口详情'元凤一路与建元路辅路, 方向: 北, 距离: 52.8139m'与风险点描述中的'下穿'关键词相符，表明该积水点位于元朔路下穿区域。","['元朔路', '下穿']",146,未央区,元朔路,"路口:元凤一路与建元路辅路,方向:北,距离:52.8139m",108.942799,34.382699
False,,,0.0,经过对比分析，CSV中的积水点位于草滩四路与河堤路的交叉口，但Excel标准风险点列表中并没有直接匹配的记录。虽然草滩四路在列表中出现过，但交叉口描述和具体位置（河堤路）没有直接对应的风险点。,"['草滩四路', '河堤路', '交叉口']",237,未央区,草滩四路,"路口:河堤路与草滩四路,方向:南,距离:360.337m",108.885906,34.386501
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'东风路'，而Excel列表中没有直接匹配的街道名称。路口详情为'环湖西路与武德路，方向：东，距离：93.1694m'，但Excel列表中没有包含这样的具体路口描述。虽然未央区是匹配的，但没有更具体的匹配要素，因此无法确定匹配。,[],148,未央区,东风路,"路口:环湖西路与武德路,方向:东,距离:93.1694m",108.978307,34.393658
True,79.0,XA197,0.9,CSV中的积水点位于西三环辅路与丰产路交汇以南，这与Excel中编号为XA197的风险点描述'西三环辅路与丰产路交汇以南'完全匹配。虽然CSV中的积水点没有提到'下穿'或'立交'等关键词，但地理位置和路口描述的匹配度很高，因此具有较高的置信度。,"['西三环辅路', '丰产路', '交汇', '以南']",239,未央区,丰产路,"路口:绕城东辅道与绕城东辅道辅路,方向:西北,距离:128.782m",108.817763,34.341548
False,,,0.0,经过对比分析，CSV中的积水点位于尚稷路与鸿明路交叉口，但该交叉口信息在Excel标准风险点列表中没有直接匹配项。虽然尚稷路在列表中多次出现，但具体的交叉口描述和地理位置信息不匹配。,"['尚稷路', '鸿明路', '交叉口']",238,未央区,尚稷路,"路口:尚稷路与鸿明路,方向:西,距离:834.293m",108.824899,34.36117
True,53.0,XA201,1.0,CSV中的积水点位于沣东大道，而Excel中的风险点编号XA201的风险点描述为'沣东大道沣东文化广场'，两者完全匹配。,"['沣东大道', '沣东文化广场']",243,未央区,沣东大道,"路口:沣东大道与文创二路,方向:东,距离:193.742m",108.80987,34.260339
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为沣惠路，而Excel风险点列表中没有直接提及沣惠路。虽然CSV中的路口详情提到了红光路与沣东大道的交叉口，但Excel风险点列表中没有直接匹配的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],245,未央区,沣惠路,"路口:红光路与沣东大道,方向:东北,距离:358.678m",108.835914,34.261231
True,56.0,XA277,1.0,CSV中的积水点位于未央区三桥街道富源二路，与Excel标准风险点列表中的编号为XA277的风险点描述'沣东南路与富源二路交汇以东'完全匹配，且地理位置和街道名称均一致。,"['未央区', '三桥街道', '富源二路', '沣东南路与富源二路交汇以东']",244,未央区,富源二路,"路口:西三环辅路与沣东南路,方向:西南,距离:69.6634m",108.828778,34.253739
True,17.0,XA124,0.95,CSV中的积水点位于未央区三桥街道阿房一路，与Excel标准风险点列表中的编号为XA124的风险点描述'三桥新（立交桥下）'高度匹配，且地理位置和街道名称均一致。,"['未央区', '三桥街道', '阿房一路', '立交桥下']",246,未央区,阿房一路,"路口:阿房一路与富源一路,方向:西,距离:57.9734m",108.827708,34.269793
True,50.0,XA200,1.0,CSV中的积水点位于征和四路与天台路交汇处，与Excel中编号为XA200的风险点描述完全一致。,"['征和四路与天台路交汇', '积水点']",248,未央区,天台路,"路口:征和四路与天台路辅路,方向:西,距离:91.8705m",108.816292,34.28674
True,17.0,XA124,0.95,CSV中的积水点位于未央区三桥街道阿房一路，与Excel标准风险点列表中的编号为XA124的风险点描述'三桥新（立交桥下）'高度匹配。虽然CSV中的具体路口信息与Excel中的描述不完全一致，但考虑到地理位置和街道名称的匹配，以及'立交'这一关键词的匹配，可以认为这是一个高置信度的匹配。,"['未央区', '三桥街道', '阿房一路', '立交']",247,未央区,阿房一路,"路口:阿房二路与宜家路,方向:南,距离:586.423m",108.841979,34.269615
True,17.0,XA124,0.95,CSV中的积水点位于未央区三桥街道西咸路，与Excel标准风险点列表中的编号XA124对应的风险点'三桥新（立交桥下）'在地理位置和描述上高度匹配。虽然CSV中的具体路口是三桥新街与西咸路交叉口，而Excel中的风险点描述为'三桥新（立交桥下）'，但考虑到三桥新街与西咸路的交叉口很可能存在立交桥，因此可以认为这是一个高置信度的匹配。,"['未央区', '三桥街道', '西咸路', '三桥新街与西咸路交叉口', '立交桥下']",250,未央区,西咸路,"路口:三桥新街与西咸路,方向:西南,距离:134.604m",108.809513,34.300029
True,17.0,XA124,0.95,CSV中的积水点位于未央区三桥街道西咸路，路口详情为三桥新街辅路与晨光路交汇，与Excel中编号为XA124的风险点描述'三桥新（立交桥下）'高度匹配，且地理位置和街道名称均一致。,"['未央区', '三桥街道', '西咸路', '三桥新街辅路与晨光路交汇', '立交桥下']",251,未央区,西咸路,"路口:三桥新街辅路与晨光路,方向:西,距离:148.228m",108.794707,34.306629
True,3.0,XA134,0.9,CSV中的积水点位于天章大道，而Excel中的风险点编号XA134对应的风险点为天章大道下穿，两者名称直接匹配，且CSV中的路口详情提到天章大道与丰业一路交汇，与Excel风险点描述相符。,"['天章大道', '下穿']",252,未央区,天章大道,"路口:天章大道与丰业一路,方向:南,距离:328.039m",108.81754,34.310911
True,3.0,XA134,0.95,CSV中的积水点位于天章大道，而Excel标准风险点列表中的编号为XA134的风险点描述为'天章大道下穿'，两者名称完全匹配，且CSV中的路口详情提到了天章一路与丰业一路的交叉口，这与Excel风险点描述中的下穿信息相符合。,"['天章大道', '下穿']",253,未央区,天章大道,"路口:天章一路与丰业一路,方向:北,距离:44.4814m",108.814151,34.314478
True,46.0,XA179,0.95,CSV中的积水点位于丰业大道与建章路交汇处，这与Excel中编号为XA179的风险点描述'丰业大道与建章路交汇'完全匹配。虽然CSV中未提及'立交'或'下穿'等关键词，但考虑到地理位置和街道名称的匹配，可以认为这是一个高置信度的匹配。,"['丰业大道', '建章路', '交汇']",254,未央区,丰业大道,"路口:天章三路与丰全路,方向:东,距离:197.694m",108.80541,34.324468
True,47.0,XA179,1.0,CSV中的积水点位于建章路与丰业大道交汇处，与Excel中编号为XA179的风险点描述'丰业大道与建章路交汇'完全匹配。,"['建章路', '丰业大道', '交汇']",255,未央区,丰业大道,"路口:建章路与丰业大道,方向:西,距离:87.909m",108.83627,34.322862
True,18.0,XA179,0.9,CSV中的积水点位于未央区三桥街道建章路，与Excel中编号为XA179的风险点描述'丰业大道与建章路交汇'高度匹配，且Excel风险点描述中包含'建章路'这一关键词，因此匹配度较高。,"['未央区', '三桥街道', '建章路', '丰业大道与建章路交汇']",257,未央区,建章路,"路口:西三环辅路与后围路,方向:东北,距离:170.275m",108.839838,34.294143
True,17.0,XA124,0.95,CSV中的积水点位于三桥街道，主要街道为建章路，与Excel中编号为XA124的风险点描述'三桥新（立交桥下）'高度匹配。虽然CSV中的具体路口信息与Excel风险点描述不完全一致，但考虑到地理位置和街道名称的匹配，可以认为这是一个高置信度的匹配。,"['三桥街道', '建章路', '三桥新（立交桥下）']",258,未央区,建章路,"路口:西宫门街与西宫墙路,方向:西,距离:299.853m",108.841979,34.301991
True,17.0,XA124,0.95,CSV中的积水点位于未央区三桥街道建章路与后围路交叉口，距离建章路312.548米，这与Excel中编号为XA124的风险点描述'三桥新（立交桥下）'高度匹配，且地理位置和街道名称均一致。,"['未央区', '三桥街道', '建章路', '后围路', '立交桥下']",259,未央区,建章路,"路口:建章路与后围路,方向:北,距离:312.548m",108.835557,34.295926
False,198.0,XA198,1.0,CSV中的积水点位于天章大道与丰安路交叉口，与Excel风险点列表中的编号为XA198的风险点描述完全一致，即天章大道与丰安路十字。,"['天章大道', '丰安路', '交叉口']",260,未央区,天章大道,"路口:天章大道与丰安路,方向:西,距离:148.898m",108.817719,34.328214
True,78.0,XA197,1.0,CSV中的积水点位于西三环辅路与丰产路交汇处，与Excel中编号为XA197的风险点描述'西三环辅路与丰产路交汇以南'完全匹配。,"['西三环辅路', '丰产路', '交汇', '以南']",261,未央区,丰产路,"路口:丰衍路与西三环辅路,方向:北,距离:268.426m",108.842336,34.330533
False,,,0.0,经过对比，CSV中的积水点位于尚稷路与草滩二路交汇处，而Excel标准风险点列表中并没有直接匹配的记录。虽然存在编号为XA203的风险点，其描述为尚稷路与草滩二路交汇以西，但该风险点被标记为橙色，与CSV中积水点的风险等级不符。因此，没有找到完全匹配的记录。,[],262,未央区,尚稷路,"路口:草滩二路与尚稷路,方向:西,距离:72.4874m",108.899597,34.380926
True,75.0,XA245,1.0,CSV中的主要街道'尚宏路'与Excel风险点中的'尚宏路'完全匹配，且CSV中的路口详情'尚宏路与尚稷路交汇以南'与Excel风险点中的'尚宏路与尚稷路交汇以南'完全匹配，同时Excel风险点中的关键词'桥'与CSV中的'高铁桥下'相关联，因此可以确定匹配。,"['街道名称匹配', '路口匹配', '关键词匹配']",263,未央区,尚宏路,"路口:尚稷路与尚宏路辅路,方向:南,距离:138.767m",108.911637,34.383423
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为尚苑路，而Excel风险点列表中没有直接匹配的街道名称。虽然CSV中提到了尚苑路与草滩四路的交叉口，但Excel风险点列表中没有提及这个交叉口。因此，无法确定积水点与风险点的匹配关系。,[],264,未央区,尚苑路,"路口:尚苑路与草滩四路,方向:西南,距离:52.26m",108.890053,34.372988
False,,,0.0,经过对比，CSV中的积水点位于尚苑路与草滩六路南段交叉口，但该交叉口信息在Excel标准风险点列表中没有直接匹配项。虽然尚苑路在列表中存在，但交叉口信息不完全匹配，且没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，因此无法确定匹配。,"['尚苑路', '草滩六路南段']",265,未央区,尚苑路,"路口:尚苑路与草滩六路南段,方向:东北,距离:93.8881m",108.879172,34.368796
False,,,0.0,经过对比，CSV中的积水点位于罗高路与北三环交叉口，但Excel标准风险点列表中并没有直接匹配的记录。虽然罗高路在列表中出现过，但交叉口描述和具体位置不匹配。,"['罗高路', '北三环', '交叉口']",266,未央区,罗高路,"路口:罗高路与北三环,方向:西,距离:207.031m",108.884791,34.355239
False,,,0.0,经过对比，CSV中的积水点位于元凤二路，而Excel标准风险点列表中并没有直接提及元凤二路。虽然CSV中的路口详情提到了秦汉大道与明光路，但Excel风险点列表中并没有直接匹配的交叉口描述。因此，无法确定CSV中的积水点与Excel标准风险点列表中的某个风险点匹配。,[],284,未央区,元凤二路,"路口:秦汉大道与明光路,方向:北,距离:220.422m",108.923299,34.381846
False,,,0.0,经过对比分析，CSV中的积水点位于未央区六村堡街道尚稷路，与Excel标准风险点列表中的任何风险点都没有完全匹配的街道名称、路口信息或关键词。虽然CSV中的路口详情提到了明光路与尚稷路的交叉口，但Excel风险点列表中没有直接提及这一交叉口。因此，无法确定积水点与任何风险点有直接关联。,[],285,未央区,尚稷路,"路口:明光路与尚稷路,方向:东北,距离:268.845m",108.922318,34.390052
True,17.0,XA124,1.0,CSV中的积水点位于未央区三桥街道三桥新街，且路口详情为西三环辅路与西三环路交汇处，距离229.863米。这与Excel标准风险点列表中的编号为XA124的风险点描述'三桥新（立交桥下）'完全匹配。,"['未央区', '三桥街道', '三桥新街', '西三环辅路与西三环路交汇', '立交桥下']",279,未央区,三桥新街,"路口:西三环辅路与西三环路,方向:南,距离:229.863m",108.839905,34.288507
False,,,0.0,经过对比分析，CSV中的积水点位于河堤路与明光路交叉口，但该交叉口并未在Excel标准风险点列表中直接提及。虽然明光路在列表中多次出现，但河堤路并未被提及，且CSV中的积水点并未与列表中的任何风险点名称、交叉口描述或关键词完全匹配。,[],286,未央区,[],"路口:河堤路与明光路,方向:南,距离:125.019m",108.917769,34.395938
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。虽然CSV中的'八府庄园商业街'与Excel中的'八府庄隧道'有相似之处，但其他信息如'路口详情'和'行政区'等均不匹配。因此，无法确定两者之间的直接关联。,"['八府庄园商业街', '八府庄隧道']",25,新城区,八府庄园商业街,"路口:八府庄园商业街与八府庄二坊,方向:北,距离:167.235m",108.973836,34.293928
True,8.0,XA183,1.0,"CSV中的街道名称'解放门街道'与Excel风险点中的'西七路与北新路十字'匹配，且CSV中的路口详情'路口:北新街与西七路,方向:东,距离:64.951m'与Excel风险点中的'西七路与北新路十字'完全匹配，同时CSV中的行政区'新城区'与Excel风险点列表的行政区一致。","['街道名称', '路口详情', '行政区']",43,新城区,西七路,"路口:北新街与西七路,方向:东,距离:64.951m",108.949813,34.275297
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为自强路街道，主要街道为二马路，而Excel风险点列表中没有直接匹配的街道名称。路口详情为西闸口与自强东路辅路，但Excel风险点列表中没有包含这样的交叉口描述。虽然CSV中的经纬度位于新城区，但地理位置的匹配不足以确定具体的匹配风险点。,[],42,新城区,二马路,"路口:西闸口与自强东路辅路,方向:西南,距离:68.3984m",108.948936,34.281347
False,,,0.0,经过分析，CSV中的积水点位于西五路，但Excel标准风险点列表中没有直接匹配的道路名称。虽然CSV中提到了西五路，但Excel中的风险点没有直接提及该道路。此外，CSV中的路口详情为西五路与西五路辅路交叉口，而Excel中的风险点没有提及辅路信息。因此，无法直接匹配。,[],44,新城区,西五路,"路口:西五路与西五路辅路,方向:南,距离:52.4278m",108.949813,34.271176
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的积水点位于东五路，而Excel列表中的风险点主要集中在隧道、立交、桥等特殊地点，没有与东五路直接相关的风险点。虽然CSV中提到了环城东路北段辅路与东五路的交叉口，但Excel列表中没有提及该交叉口或相关风险点。,[],51,新城区,东五路,"路口:环城东路北段辅路与东五路,方向:东,距离:59.5109m",108.968137,34.271439
False,,,0.0,经过分析，CSV中的积水点位于长缨西路与环城东路北段辅路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然长缨西路与幸福路（长乐路-长缨路）隧道相邻，但CSV中的积水点并不位于隧道内，因此不能直接匹配。此外，CSV中的积水点没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，因此无法通过关键词匹配。考虑到地理位置信息，CSV中的积水点位于新城区，但Excel风险点列表中并没有直接匹配的行政区划信息。因此，无法确定积水点与Excel标准风险点列表中的任何风险点匹配。,[],52,新城区,长缨西路,"路口:环城东路北段辅路与长缨西路辅路,方向:西北,距离:1.65456m",108.968137,34.278716
False,,,0.0,经过分析，CSV中的积水点位于万寿中路与咸宁中路辅路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然万寿路和咸宁中路在列表中存在，但交叉口描述不完全匹配，且没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，因此无法确定匹配。,[],65,新城区,万寿中路,"路口:万寿路辅路与咸宁中路辅路,方向:东北,距离:44.8514m",109.009651,34.252984
False,,,0.0,经过分析，CSV中的积水点位于东八路与尚俭路/环城北路东段辅路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然存在“隧道”、“下穿”、“立交”、“桥”等关键词，但它们并不直接对应CSV中的积水点。因此，无法确定积水点与Excel中的风险点有直接关联。,[],53,新城区,东八路,"路口:尚俭路与环城北路东段辅路,方向:东北,距离:83.2418m",108.961298,34.278541
True,6.0,XA154,1.0,CSV中的路口详情明确指出路口为咸宁中路与公园南路，这与Excel中编号为XA154的风险点描述'咸宁中路与公园南路十字'完全匹配。,"['咸宁中路与公园南路', '路口']",66,新城区,公园南路,"路口:咸宁中路与公园南路,方向:东北,距离:9.40707m",109.001585,34.252633
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为长缨西路，但Excel列表中没有直接匹配的街道名称。虽然CSV中提到了华清西路与康复路的交叉口，但Excel列表中没有包含这个交叉口的描述。因此，没有找到完全匹配的风险点。,[],80,新城区,长缨西路,"路口:华清西路与康复路,方向:南,距离:107.977m",108.979666,34.28139
True,5.0,XA7,0.9,CSV中的积水点位于幸福中路与韩森路交叉口，CSV中提到的路口详情为韩森路与韩森东路交叉口，虽然街道名称不完全一致，但考虑到韩森路和韩森东路是相邻的道路，且CSV中提到的幸福中路与Excel风险点中的幸福路相匹配，同时CSV中提到了'隧道'这一关键词，与Excel风险点中的'幸福路韩森隧道'相匹配，因此判断为匹配。,"['幸福中路', '韩森路', '隧道', '交叉口']",67,新城区,幸福中路,"路口:韩森路与韩森东路,方向:西北,距离:111.55m",109.01044,34.26105
False,,,0.0,经过分析，CSV中的积水点位于长乐中路街道，幸福北路，与Excel标准风险点列表中的任何风险点都没有直接匹配的街道名称或路口详情。虽然CSV中提到了长缨路与幸福路辅路，但Excel风险点列表中没有直接对应的风险点。因此，无法确定积水点与风险点的匹配关系。,[],81,新城区,幸福北路,"路口:长缨路与幸福路辅路,方向:南,距离:379.402m",109.010878,34.275516
False,,,0.0,经过分析，CSV中的积水点位于长乐中路街道，幸福北路，与Excel标准风险点列表中的任何风险点都没有完全匹配的街道名称或路口描述。虽然CSV中提到了长缨路与幸福路辅路，但在Excel风险点列表中没有直接对应的风险点。因此，无法确定积水点与风险点的匹配关系。,[],87,新城区,幸福北路,"路口:长缨路与幸福路辅路,方向:北,距离:204.102m",109.011054,34.280733
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为含元路，而Excel列表中的风险点主要涉及隧道、立交、桥等，且没有含元路的相关记录。虽然CSV中的路口详情提到了华清东路辅路与华清路立交，但Excel列表中没有直接对应的风险点。因此，无法确定匹配。,[],88,新城区,含元路,"路口:华清东路辅路与华清路立交,方向:西北,距离:287.38m",108.990582,34.289106
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。虽然CSV中的'华清东路'与Excel中编号为XA194的风险点'华清东路与公园北路交汇以东'存在部分匹配，但CSV中的'酒一路与十里铺南路'路口信息与Excel中的描述不匹配，且CSV中的'南'方向和'距离:239.211m'信息在Excel中未提及。因此，无法确定两者为同一风险点。,['华清东路'],85,新城区,华清东路,"路口:酒一路与十里铺南路,方向:西,距离:239.211m",109.017279,34.289325
True,15.0,XA254,1.0,CSV中的主要街道'北关正街'与Excel风险点列表中的编号XA254对应的风险点'北关正街与振华路交汇'完全匹配，且CSV中的路口详情'路口:北关正街与二马路'与Excel风险点描述中的交叉口信息一致。,"['北关正街', '北关正街与二马路', '振华路交汇']",40,莲湖区,北关正街,"路口:北关正街与二马路,方向:南,距离:67.3803m",108.942624,34.285292
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不完全匹配。CSV中的积水点位于北关街道，环城北路，路口为北门盘道与北关正街辅路，但Excel列表中的风险点没有直接提及这些具体信息。虽然北关正街与Excel中的北关正街与振华路交汇存在部分匹配，但路口信息不完全一致，因此无法确定匹配。,"['北关街道', '环城北路', '北门盘道与北关正街辅路']",41,莲湖区,环城北路,"路口:北门盘道与北关正街辅路,方向:东北,距离:53.0542m",108.942536,34.278979
True,19.0,XA32,0.95,CSV中的积水点位于星火路，且路口详情中提到环城西路北段与星火路的交叉口，这与Excel中编号为XA32的风险点描述星火路隧道相匹配。虽然CSV中没有直接提到'隧道'这个词，但根据地理位置和街道名称的匹配，可以推断出两者是同一地点。,"['星火路', '环城西路北段', '隧道']",46,莲湖区,星火路,"路口:环城西路北段与星火路,方向:北,距离:220.624m",108.918075,34.279242
True,10.0,XA157,0.9,CSV中的主要街道'星火路'与Excel风险点列表中的'大兴东路'不完全匹配，但考虑到路口详情中提到'大兴东路与星火路辅路'，可以推断积水点位于大兴东路附近。Excel风险点列表中的'XA157'对应的风险点为'大兴东路'，虽然不是完全精确匹配，但考虑到地理位置的邻近性，可以认为这是一个高置信度的匹配。,"['星火路', '大兴东路', '路口详情']",45,莲湖区,星火路,"路口:大兴东路与星火路辅路,方向:东,距离:26.6346m",108.917724,34.28801
True,18.0,XA233,1.0,CSV中的积水点位于红庙坡街道自强西路，路口详情为自强西路与工农路交叉口，方向为东，距离为43.887m。这与Excel标准风险点列表中的编号为XA233的风险点描述完全匹配，该风险点位于工农路与自强西路十字。,"['红庙坡街道', '自强西路', '工农路', '交叉口']",47,莲湖区,自强西路,"路口:自强西路与工农路,方向:东,距离:43.887m",108.928946,34.281697
True,3.0,XA31,0.9,CSV中的积水点位于红庙坡街道，且路口详情中提到尚武门立交与西北三路，这与Excel中编号为XA31的风险点汉城路陇海线隧道相符，虽然不完全匹配，但考虑到地理位置和关键词的相似性，可以认为是一个高置信度的匹配。,"['红庙坡街道', '尚武门立交', '陇海线隧道']",48,莲湖区,环城北路西段,"路口:尚武门立交与西北三路,方向:东北,距离:46.428m",108.928771,34.278453
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于环城西路北段，大庆路与莲湖路交叉口东南方向，距离177.394米。然而，Excel列表中的风险点没有直接提及环城西路北段或大庆路与莲湖路交叉口。虽然存在一些关键词如'大庆路'，但没有找到完全匹配的记录。,"['环城西路北段', '大庆路', '莲湖路']",49,莲湖区,环城西路北段,"路口:大庆路与莲湖路,方向:东南,距离:177.394m",108.918601,34.269774
False,,,0.0,经过分析，CSV中的积水点位于莲湖区北院门街道西大街，路口为龙渠湾与菜坑岸交汇处，西北方向距离64.4824米。然而，Excel标准风险点列表中并没有直接匹配的街道名称、路口描述或关键词。虽然存在一些风险点如玉祥门隧道、小北门立交等，但它们与CSV中的积水点地理位置不符。,[],55,莲湖区,西大街,"路口:龙渠湾与菜坑岸,方向:西北,距离:64.4824m",108.921319,34.260305
True,17.0,XA231,1.0,"CSV中的积水点位于莲湖区西关街道劳动南路，与Excel标准风险点列表中的编号为XA231的风险点'丰镐东路与劳动路交汇'完全匹配。该风险点描述了丰镐东路与劳动路的交汇处，与CSV中的路口详情'路口:劳动路与丰镐东路辅路,方向:东南,距离:50.051m'相符。","['莲湖区', '西关街道', '劳动南路', '丰镐东路与劳动路交汇']",56,莲湖区,劳动南路,"路口:劳动路与丰镐东路辅路,方向:东南,距离:50.051m",108.905712,34.260831
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。具体分析如下：
1. 街道名称匹配：CSV中的主要街道为莲湖路，而Excel风险点列表中没有直接匹配的街道名称。
2. 路口匹配：CSV中的路口详情为莲湖路辅路与北马道巷交叉口，方向为东，距离为46.5458m，Excel风险点列表中没有直接匹配的路口描述。
3. 关键词匹配：CSV中的路口描述中没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，而Excel风险点列表中的风险点描述中包含这些关键词。
4. 地理位置匹配：CSV中的行政区为莲湖区，与Excel风险点列表中的莲湖区匹配，但不足以确定具体匹配的风险点。
5. 精确匹配优先：由于没有直接匹配的记录，因此无法确定匹配的Excel记录索引和编号。",[],50,莲湖区,莲湖路,"路口:莲湖路辅路与北马道巷,方向:东,距离:46.5458m",108.921494,34.271001
True,17.0,XA231,1.0,CSV中的积水点位于丰镐东路，与Excel风险点列表中的丰镐东路与劳动路交汇匹配，且路口详情中提到了丰镐东路，因此可以确定匹配。,"['丰镐东路', '劳动路交汇']",57,莲湖区,丰镐东路,"路口:桃园路与居德一巷,方向:东南,距离:50.6722m",108.896857,34.260831
True,6.0,XA232,0.95,CSV中的积水点位于桃园北路，且路口详情提到了西斜路与桃园北路辅路，这与Excel中编号为XA232的风险点描述'桃园北路与西斜路交汇以南'高度匹配。虽然CSV中的积水点位于西北方向，而Excel中的描述没有明确方向，但考虑到其他匹配要素，可以认为这是一个高置信度的匹配。,"['桃园北路', '西斜路与桃园北路辅路', '交汇']",58,莲湖区,桃园北路,"路口:西斜路与桃园北路辅路,方向:南,距离:151.047m",108.896857,34.27556
True,14.0,XA158,1.0,CSV中的积水点位于桃园北路，与Excel标准风险点列表中的编号为XA158的风险点（桃园北路）完全匹配。,"['街道名称：桃园北路', '行政区：莲湖区']",59,莲湖区,桃园北路,"路口:桃园北路与桃园北路辅路,方向:北,距离:341.949m",108.897734,34.285029
True,10.0,XA157,1.0,CSV中的主要街道字段'大兴东路'与Excel风险点列表中的'大兴东路'完全匹配，且CSV中的行政区'莲湖区'与Excel风险点列表中的莲湖区相符，因此可以确定匹配。,"['主要街道', '行政区']",60,莲湖区,大兴东路,"路口:大兴东路辅路与杏园北路,方向:东,距离:17.1806m",108.890895,34.288185
False,,,0.0,经过分析，CSV中的积水点位于大兴西路辅路与丰收二路交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然大兴西路是附近的一条主要道路，但Excel中的风险点没有明确提及大兴西路辅路或丰收二路。因此，无法确定积水点与Excel中的风险点有直接关联。,[],61,莲湖区,大兴西路,"路口:大兴西路辅路与丰收二路,方向:西,距离:158.479m",108.880024,34.287659
False,,,0.0,"经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'桃园路街道'，与Excel中的风险点名称没有直接对应。路口详情为'路口:丰衍路与丰收路,方向:东,距离:18.3594m'，也没有与Excel中的风险点描述直接匹配。虽然存在'隧道'和'立交'等关键词，但它们并不直接指向Excel中的任何风险点。",[],62,莲湖区,丰衍路,"路口:丰衍路与丰收路,方向:东,距离:18.3594m",108.879498,34.282048
False,,,0.0,经过分析，CSV中的积水点位于汉城北路与大兴西路辅路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然汉城北路在列表中存在，但交叉口描述和具体位置（大兴西路辅路）没有直接对应的风险点。因此，无法确定积水点与Excel标准风险点列表中的某个风险点匹配。,[],63,莲湖区,汉城北路,"路口:汉城北路与大兴西路辅路,方向:南,距离:341.57m",108.867398,34.285204
False,,,0.0,经过分析，CSV中的积水点位于大兴西路与西安高架快速干道交汇处西北方向，距离293.79米。虽然大兴西路与Excel中的多个风险点名称有重合，但具体位置和路口描述不匹配。例如，Excel中的风险点没有提及西安高架快速干道，也没有提及具体的距离信息。因此，无法确定CSV中的积水点与Excel中的任何风险点有直接匹配。,[],256,莲湖区,大兴西路,"路口:西安高架快速干道与大兴西路,方向:西,距离:293.79m",108.855179,34.291645
False,,,0.0,经过分析，CSV中的积水点位于莲湖区西关街道环城南路西段，路口为丰庆路与环城南路交汇处，距离20.331米。然而，Excel标准风险点列表中并没有直接匹配的记录。虽然丰庆路在列表中出现过，但其他匹配条件如街道名称、路口详情等均不匹配。因此，无法确定积水点与Excel中的风险点有直接关联。,[],268,莲湖区,环城南路西段,"路口:丰庆路与环城南路,方向:东北,距离:20.331m",108.918929,34.252652
False,,,0.0,经过分析，CSV中的积水点位于丰庆路与劳动南路辅路交叉口，但Excel标准风险点列表中并没有直接匹配的道路名称或交叉口描述。虽然丰庆路在Excel中作为风险点的一部分出现（编号XA36），但该风险点描述为丰庆路沿线，并未具体到交叉口。因此，无法确定CSV中的积水点与Excel中的风险点有直接匹配。,[],272,莲湖区,丰庆路,"路口:丰庆路与劳动南路辅路,方向:东,距离:97.2134m",108.906442,34.252562
False,,,0.0,经过分析，CSV中的积水点位于枣园西路与崇智路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然枣园西路与Excel中的某些风险点道路名称有部分重叠，但没有完全匹配的记录。此外，CSV中的积水点没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，因此无法通过关键词匹配。考虑到地理位置信息，也没有直接匹配的记录。,[],278,莲湖区,枣园西路,"路口:枣园西路与崇智路,方向:西南,距离:59.71m",108.851099,34.283601
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。具体分析如下：
1. CSV中的街道名称为'桃园路街道'，Excel中没有完全匹配的街道名称。
2. CSV中的路口详情为'沣惠北路与槐市巷，方向:北，距离:175.415m'，Excel中没有完全匹配的路口描述。
3. CSV中的关键词'隧道'、'下穿'、'立交'、'桥'等在Excel风险点中也没有直接对应的描述。
4. CSV中的行政区'莲湖区'与Excel风险点列表中的莲湖区匹配，但不足以确定具体匹配的风险点。
5. 由于没有精确匹配的记录，因此无法给出匹配的索引和编号。",[],276,莲湖区,大庆路,"路口:沣惠北路与槐市巷,方向:北,距离:175.415m",108.886106,34.280747
True,10.0,XA292,1.0,CSV中的积水点位于化工南巷，与Excel标准风险点列表中的编号为XA292的风险点完全匹配，该风险点描述为化工南巷，且风险等级为红色。,['化工南巷'],70,碑林区,经九路,"路口:经九路与化工南巷,方向:东北,距离:26.0066m",108.974669,34.240008
True,11.0,XA184,1.0,CSV中的积水点位于金花南路与北沙坡路交叉口，距离195.762米，这与Excel中编号为XA184的风险点描述'金花南路与北沙坡交以北'完全匹配，因此可以确定匹配。,"['金花南路与北沙坡路', '交叉口', '距离195.762m', '金花南路与北沙坡交以北']",64,碑林区,东二环路,"路口:金花南路与北沙坡路,方向:东北,距离:195.762m",108.992247,34.248206
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均无直接匹配。CSV中的主要街道为环城东路北段，而Excel风险点列表中并未包含该街道名称。虽然CSV中提到了炮房街与环城东路辅路的交叉口，但在Excel风险点列表中并未找到与之对应的交叉口描述。因此，无法确定积水点与风险点之间的匹配关系。,[],54,碑林区,环城东路北段,"路口:炮房街与环城东路辅路,方向:东,距离:34.994m",108.968312,34.261707
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的主要街道为东仓门，而Excel风险点列表中没有包含东仓门这一街道名称。虽然CSV中提到了和平路与雁塔北路的交叉口，但Excel风险点列表中没有直接提及这一交叉口。因此，无法找到完全匹配的记录。,[],72,碑林区,东仓门,"路口:和平路与雁塔北路,方向:东北,距离:52.5705m",108.958975,34.252808
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均无直接匹配。CSV中的主要街道为端履门，而Excel风险点列表中无直接对应街道名称。路口详情中的南门盘道与环城南路辅路在Excel风险点列表中也没有直接对应描述。尽管存在一些关键词如'南'、'环城'等，但不足以构成精确匹配。,[],73,碑林区,端履门,"路口:南门盘道与环城南路辅路,方向:东,距离:87.9221m",108.943982,34.251493
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不完全匹配。具体分析如下：
1. 街道名称匹配：CSV中的街道为太乙路街道，Excel中没有完全匹配的街道名称。
2. 路口匹配：CSV中的路口详情为咸宁西路辅路与太乙路交叉口，Excel中没有完全匹配的路口描述。
3. 关键词匹配：CSV中的路口描述包含'路口'和'路'，Excel中风险点描述中包含'隧道'、'下穿'、'立交'、'桥'等关键词，但CSV中没有这些关键词。
4. 地理位置：CSV中的行政区为碑林区，Excel中的风险点也位于碑林区，但具体位置不匹配。
5. 精确匹配优先：由于没有完全匹配的记录，因此无法给出精确匹配的索引和编号。",[],71,碑林区,安东街,"路口:咸宁西路辅路与太乙路,方向:东南,距离:10.519m",108.967655,34.25237
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为长安北路，但Excel列表中没有直接匹配的街道名称。虽然CSV中提到了长安北路与友谊西路辅路的交叉口，但Excel列表中没有包含此交叉口的描述。因此，无法确定具体的匹配记录。,[],75,碑林区,长安北路,"路口:长安北路与友谊西路辅路,方向:东北,距离:66.2622m",108.942404,34.243427
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。具体分析如下：
1. 街道名称匹配：CSV中的主要街道为体育馆南路，而Excel风险点列表中没有完全匹配的街道名称。
2. 路口匹配：CSV中的路口详情为长安北路与体育馆南路交叉口，方向为东北，距离为20.1618m，而Excel风险点列表中没有描述与此完全一致的路口。
3. 关键词匹配：CSV中的路口描述中没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，而Excel风险点列表中包含这些关键词的记录。
4. 地理位置：CSV中的行政区为碑林区，与Excel风险点列表中的行政区匹配，但不足以确定匹配。
5. 精确匹配优先：由于没有完全匹配的记录，因此无法确定匹配的Excel记录索引和编号。",[],74,碑林区,体育馆南路,"路口:长安北路与体育馆南路,方向:东北,距离:20.1618m",108.942667,34.247723
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的路口详情为'二环南路东段与长安路立交,方向:西北,距离:96.1551m'，而Excel列表中的风险点描述没有包含'二环南路东段'或'长安路立交'这样的具体信息。虽然长安北路是长安路的一部分，但Excel列表中没有直接匹配的街道名称。因此，无法确定积水点与风险点之间的直接关联。",[],76,碑林区,长安北路,"路口:二环南路东段与长安路立交,方向:西北,距离:96.1551m",108.943018,34.232293
True,5.0,XA24,0.95,CSV中的路口详情提到'二环南路东段辅路与雁塔立交'，这与Excel中编号为XA24的风险点'南二环雁塔立交'完全匹配。虽然CSV中的'二环南路东段'与Excel中的'南二环'略有不同，但考虑到'雁塔立交'这一关键词的匹配，可以认为这是一个高置信度的匹配。,"['二环南路东段辅路与雁塔立交', '南二环雁塔立交']",79,碑林区,二环南路东段,"路口:二环南路东段辅路与雁塔立交,方向:西,距离:291.118m",108.959852,34.232117
True,17.0,XA286,1.0,"CSV中的主要街道'兴庆路'与Excel风险点中的道路名'兴庆路'完全匹配，且CSV中的路口详情'路口:兴庆路与仁厚庄南路,方向:东北,距离:119.292m'与Excel风险点中的描述'兴庆路与仁厚庄南路交汇以南'高度匹配，同时Excel风险点中的'下穿'关键词与CSV中的'下穿隧道'描述相符。","['兴庆路', '仁厚庄南路', '下穿隧道']",233,碑林区,兴庆路,"路口:兴庆路与仁厚庄南路,方向:东北,距离:119.292m",108.984641,34.257886
True,13.0,XA287,1.0,"CSV中的主要街道'大学南路'与Excel风险点中的'大学南路'完全匹配，且路口详情'路口:太白北路与大学南路,方向:东,距离:41.6804m'与Excel风险点中的'大学南路与太白北路十字'精确匹配，符合精确匹配优先的策略。","['主要街道', '路口详情']",267,碑林区,大学南路,"路口:太白北路与大学南路,方向:东,距离:41.6804m",108.919196,34.247568
True,21.0,XA15,1.0,CSV中的主要街道'咸宁西路'与Excel风险点列表中的'咸宁西路'完全匹配，且Excel风险点列表中的'咸宁西路'风险等级为橙色，与CSV中的积水点风险等级相符。,"['主要街道', '咸宁西路']",234,碑林区,咸宁西路,"路口:思源路与梧桐东道,方向:西北,距离:105.024m",108.979646,34.252713
True,16.0,XA217,1.0,CSV中的主要街道'友谊西路'与Excel风险点列表中的'友谊西路环道'完全匹配，且路口详情中提到的'路口:友谊西路辅路与边东街'与风险点描述中的'友谊西路环道'相符，因此可以确定匹配。,"['友谊西路', '友谊西路环道', '路口:友谊西路辅路与边东街']",269,碑林区,友谊西路,"路口:友谊西路辅路与边东街,方向:西北,距离:50.5749m",108.92437,34.243019
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的红缨路和路口详情与Excel中的风险点名称和描述没有直接对应关系，且没有包含关键词如'隧道'、'下穿'、'立交'、'桥'等，因此无法确定匹配。,[],270,碑林区,红缨路,"路口:红缨路与友谊西路辅路,方向:东,距离:54.2994m",108.931773,34.242751
True,12.0,XA155,1.0,CSV中的积水点位于含光路与大学南路十字，这与Excel中编号为XA155的风险点'含光路与大学南路十字'完全匹配。,"['含光路', '大学南路', '十字']",271,碑林区,含光路,"路口:含光路北段与大学南路,方向:东,距离:49.6224m",108.928026,34.247568
False,,,0.0,经过分析，CSV中的积水点位于劳动南路与友谊西路辅路交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然友谊西路在Excel中存在，但交叉口描述不完全匹配。因此，无法确定积水点与Excel中的风险点有直接关联。,"['劳动南路', '友谊西路辅路', '交叉口']",273,碑林区,劳动南路,"路口:劳动南路与友谊西路辅路,方向:西,距离:23.7333m",108.906888,34.242751
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不完全匹配。CSV中的街道名称为'太乙路街道'，而Excel风险点列表中没有完全匹配的街道名称。虽然CSV中的路口详情提到了'二环南路东段辅路与太乙路辅路'，但Excel风险点列表中没有直接提及这一具体路口。因此，无法确定匹配的Excel记录索引和编号。,[],280,碑林区,二环南路东段,"路口:二环南路东段辅路与太乙路辅路,方向:东北,距离:48.7442m",108.968609,34.235906
False,,,0.0,"JSON解析错误: Expecting ',' delimiter: line 6 column 214 (char 307)",[],275,碑林区,朱雀大街北段,"路口:环城南路西段辅路与环城南路西段,方向:东,距离:67.6297m",108.937124,34.252562
True,5.0,XA37,1.0,CSV中的积水点位于西影路，与Excel标准风险点列表中的编号为XA37的风险点'西影路阳光小区'完全匹配，且风险等级为红色。,"['街道名称：西影路', '路口详情：路口:万寿路与西影路']",68,雁塔区,西影路,"路口:万寿路与西影路,方向:西,距离:141.418m",109.007897,34.240359
True,5.0,XA37,0.95,CSV中的积水点位于西影路，而Excel中的风险点编号XA37对应的风险点为西影路阳光小区，两者街道名称完全匹配，且CSV中的路口详情提到了雁翔路与西影路的交叉口，与Excel风险点描述相符。,"['街道名称匹配：西影路', '路口匹配：雁翔路与西影路交叉口']",69,雁塔区,西影路,"路口:雁翔路与西影路,方向:东北,距离:58.7903m",108.989661,34.232643
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的街道名称为小寨路街道，而Excel风险点列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了长安中路与长安北路辅路，但Excel风险点列表中没有包含这样的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],77,雁塔区,长安中路,"路口:长安中路与长安北路辅路,方向:东南,距离:172.142m",108.94293,34.229662
True,19.0,XA295,1.0,CSV中的积水点位于育才路与翠华路口，与Excel中编号为XA295的风险点描述完全一致。,"['育才路', '翠华路', '路口']",78,雁塔区,翠华路,"路口:翠华路与育才路,方向:东北,距离:12.9243m",108.951961,34.22826
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'四季东巷'，而Excel列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了'吉祥路与四季东巷'，但Excel列表中没有包含这个具体的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],151,雁塔区,四季东巷,"路口:吉祥路与四季东巷,方向:南,距离:70.9875m",108.92486,34.223815
False,,,0.0,经过对比，CSV中的积水点位于雁塔区小寨路街道，长安中路与西小寨西路交叉口西南方向109.447米处。然而，Excel标准风险点列表中并没有直接匹配的街道名称或路口描述。虽然小寨十字（编号XA44）位于长安中路附近，但CSV中的积水点位置与该风险点不完全一致，因此无法确定匹配。,[],150,雁塔区,长安中路,"路口:长安中路与小寨西路,方向:南,距离:109.447m",108.942163,34.223547
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均无直接匹配。CSV中的街道名称为小寨路街道，而Excel风险点列表中无此街道名称的直接匹配。路口详情为教学东路与梧桐路交叉口，北方向，距离148.811米，但在Excel风险点列表中未找到完全匹配的路口描述。尽管存在一些关键词如'教学东路'和'梧桐路'，但它们并未与任何风险点的描述直接对应。因此，无法确定积水点与风险点之间的匹配关系。,[],152,雁塔区,雁塔西路,"路口:教学东路与梧桐路,方向:北,距离:148.811m",108.937347,34.219534
False,,,0.0,CSV中的积水点位于长安南路与昌明路的交叉口，但该交叉口在Excel标准风险点列表中没有直接匹配的记录。虽然长安南路是雁塔区的主要道路，但交叉口的具体描述（长安南路与昌明路）没有在Excel风险点列表中找到。因此，无法确定是否存在匹配。,"['长安南路', '昌明路']",153,雁塔区,长安南路,"路口:长安南路与昌明路,方向:东南,距离:30.7296m",108.942342,34.210525
True,9.0,XA151,1.0,CSV中的主要街道'含光路南段'与Excel风险点中的'含光路'完全匹配，且CSV中的路口详情'路口:崇业路与含光路南段'与Excel风险点中的'含光路与崇业路十字'描述匹配，同时CSV中的行政区'雁塔区'与Excel风险点列表的雁塔区匹配，因此可以确定匹配。,"['含光路', '崇业路十字', '雁塔区']",154,雁塔区,含光路南段,"路口:崇业路与含光路南段,方向:东,距离:71.5571m",108.928071,34.228988
True,9.0,XA151,0.9,CSV中的积水点位于崇业路，与Excel中编号为XA151的风险点'含光路与崇业路十字'的街道名称完全匹配。虽然CSV中的路口详情与Excel风险点描述不完全一致，但考虑到崇业路是风险点的一部分，且没有更精确的匹配，因此认为这是一个高置信度的匹配。,"['崇业路', '含光路与崇业路十字']",155,雁塔区,崇业路,"路口:崇业路与崇业北巷,方向:东南,距离:37.2112m",108.931282,34.22872
True,15.0,XA117,1.0,CSV中的街道名称'科技四路'与Excel风险点中的'科技四路'完全匹配，且路口详情'科技四路与团结南路'与Excel风险点中的'团结南路与科技四路十字'匹配，路口方向和距离也一致。,"['街道名称', '路口详情']",156,雁塔区,科技四路,"路口:科技四路与团结南路,方向:东南,距离:46.4135m",108.877589,34.221496
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为文兴巷，而Excel风险点列表中没有直接提及文兴巷。虽然CSV中的路口详情提到了唐延路与科技路，但在Excel风险点列表中并没有找到包含这两个道路名称的交叉口描述。因此，无法确定积水点与风险点之间的匹配关系。,[],157,雁塔区,文兴巷,"路口:唐延路与科技路,方向:东,距离:21.8934m",108.885348,34.238799
False,,,0.0,经过对比，CSV中的积水点位于文兴巷与团结南路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然团结南路在列表中存在，但文兴巷并未直接列出。因此，无法确定是否存在精确匹配。,"['文兴巷', '团结南路']",159,雁塔区,文兴巷,"路口:文兴巷与团结南路,方向:西南,距离:52.5125m",108.877053,34.241653
True,7.0,XA170,0.9,CSV中的积水点位于雁塔区丈八街道科技路，且路口详情为科技路与团结南路交汇处，这与Excel中编号为XA170的风险点描述‘团结南路与科技路交汇以南’高度匹配，考虑到地理位置和街道名称的匹配，匹配置信度较高。,"['雁塔区', '丈八街道', '科技路', '团结南路', '交汇']",158,雁塔区,科技路,"路口:科技路与团结南路,方向:东南,距离:169.206m",108.878124,34.23755
True,19.0,XA118,0.9,CSV中的积水点位于雁塔区鱼化寨街道科技西路，与Excel标准风险点列表中的编号为XA118的风险点描述'西安外事学院北教学区医学院科技西路'高度匹配，且地理位置和街道名称均一致。,"['雁塔区', '鱼化寨街道', '科技西路', '西安外事学院北教学区医学院']",160,雁塔区,科技西路,"路口:科技西路与多元路,方向:东,距离:129.72m",108.857877,34.238888
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为鱼斗路，而Excel风险点列表中没有直接提及鱼斗路。虽然鱼化寨街道在Excel列表中存在，但路口详情与列表中的任何交叉口描述都不相符。因此，无法确定匹配。,[],161,雁塔区,鱼斗路,"路口:鱼化一路与科技二路,方向:西,距离:120.441m",108.861088,34.227293
True,36.0,XA279,1.0,"CSV中的路口详情与Excel风险点中的描述完全匹配，具体为：
CSV: 路口:科技四路与鱼化三路, 方向:东南, 距离:53.1563m
Excel: 鱼化三路与科技四路十字
两者路口名称和道路名称完全一致，且CSV中的方向和距离信息与Excel描述相符。","['科技四路与鱼化三路', '鱼化三路与科技四路十字', '东南', '53.1563m']",162,雁塔区,丈八北路,"路口:科技四路与鱼化三路,方向:东南,距离:53.1563m",108.858947,34.221317
True,13.0,XA285,1.0,"CSV中的主要街道'丈八北路'与Excel风险点中的'化龙一路与科技五路'完全匹配，且路口详情'路口:化龙一路与科技五路,方向:东,距离:26.3912m'与Excel风险点中的'化龙一路与科技五路交汇'描述一致，因此匹配成功。","['丈八北路', '化龙一路与科技五路', '路口:化龙一路与科技五路']",163,雁塔区,丈八北路,"路口:化龙一路与科技五路,方向:东,距离:26.3912m",108.852615,34.218107
True,8.0,XA116,1.0,"CSV中的主要街道'鱼化一路'与Excel风险点中的'科技六路与鱼化二路交汇'存在直接匹配，且CSV中的路口详情'路口:鱼化二路与科技六路,方向:东北,距离:24.9148m'与Excel风险点中的描述完全一致。","['主要街道', '路口详情']",164,雁塔区,鱼化一路,"路口:鱼化二路与科技六路,方向:东北,距离:24.9148m",108.862158,34.213379
False,,,0.0,经过对比，CSV中的积水点位于科技六路，但Excel标准风险点列表中没有直接匹配的道路名称。虽然CSV中的路口详情提到了高新六路与科技六路辅路，但在Excel风险点列表中并没有找到包含这两个道路名称的交叉口描述。因此，无法确定CSV积水点与Excel标准风险点列表中的某个风险点匹配。,[],165,雁塔区,科技六路,"路口:高新六路与科技六路辅路,方向:东北,距离:36.5628m",108.872861,34.213469
True,14.0,XA224,1.0,CSV中的主要街道'丈八二路'与Excel风险点中的'丈八西路与丈八二路交汇'完全匹配，且路口详情中的'路口:丈八西路与丈八二路'与Excel风险点描述一致，没有模糊匹配，因此匹配度高。,"['丈八二路', '丈八西路与丈八二路交汇']",166,雁塔区,丈八二路,"路口:丈八西路与丈八二路,方向:东,距离:71.9208m",108.876161,34.200179
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为鱼化寨街，而Excel风险点列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了西三环辅路与天谷四路，但Excel风险点列表中没有包含这一具体路口的描述。因此，无法确定积水点与风险点的匹配关系。,[],168,雁塔区,鱼化寨街,"路口:西三环辅路与天谷四路,方向:西,距离:137.195m",108.836047,34.225889
True,42.0,XA226,1.0,CSV中的主要街道'丈八东路'与Excel风险点中的'丈八东路'完全匹配，且CSV中的路口详情'丈八东路辅路与西华林路'与Excel风险点中的'丈八东路与西华林路交汇'匹配，符合精确匹配优先的策略。,"['丈八东路', '丈八东路辅路与西华林路', '交汇']",167,雁塔区,丈八东路,"路口:丈八东路辅路与西华林路,方向:东,距离:61.5813m",108.895427,34.20009
False,,,0.0,经过对比，CSV中的积水点位于鱼化寨街道鱼斗路，与Excel标准风险点列表中的任何一条记录都没有完全匹配的街道名称或路口信息。虽然鱼化寨街道与鱼化二路交汇处（编号XA116）有部分重合，但路口详情和具体位置不匹配。,"['鱼化寨街道', '鱼斗路']",169,雁塔区,鱼斗路,"路口:云水一路与天谷六路,方向:北,距离:187.168m",108.832168,34.219712
False,,,0.0,经过对比，CSV中的积水点位于云水二路，而Excel标准风险点列表中并没有直接匹配的街道名称。虽然天谷八路与云水二路的交叉口在列表中存在，但CSV中的积水点并未直接与任何一个风险点的街道名称或交叉口描述完全匹配。因此，无法确定积水点与Excel标准风险点列表中的某个风险点匹配。,[],171,雁塔区,云水二路,"路口:天谷八路与云水二路,方向:东北,距离:117.195m",108.828243,34.210213
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为鱼斗路，而Excel风险点列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了云水二路与天谷四路，但Excel风险点列表中没有包含这两个道路名称的交叉口描述。因此，无法确定匹配。,[],170,雁塔区,鱼斗路,"路口:云水二路与天谷四路,方向:东北,距离:29.1865m",108.828645,34.225732
True,33.0,XA33,1.0,CSV中的主要街道'锦业路'与Excel风险点列表中的'锦业路'完全匹配，且CSV中的行政区'雁塔区'与Excel风险点列表中的雁塔区匹配，因此可以确定匹配。,"['主要街道', '行政区']",172,雁塔区,锦业路,"路口:锦业路与云水三路辅路,方向:西北,距离:21.1785m",108.823427,34.199376
False,,,0.0,经过对比，CSV中的积水点位于丈八八路，但Excel标准风险点列表中没有直接匹配的道路名称。虽然CSV中的路口详情提到了云水一路与西户路的交叉口，但Excel风险点列表中没有包含这个具体的交叉口描述。因此，无法找到完全匹配的记录。,[],173,雁塔区,丈八八路,"路口:云水一路与西户路,方向:东南,距离:157.308m",108.833238,34.201428
False,,,0.0,经过对比，CSV中的积水点位于丈八六路，而Excel标准风险点列表中没有直接匹配的道路名称。虽然CSV中的路口详情提到了科技八路辅路与西户路，但在Excel风险点列表中没有找到包含这两个道路名称的交叉口描述。因此，无法直接匹配。,[],174,雁塔区,丈八六路,"路口:科技八路辅路与西户路,方向:东,距离:37.8241m",108.841444,34.203747
True,33.0,XA33,1.0,CSV中的主要街道'锦业路'与Excel风险点列表中的'锦业路'完全匹配，且CSV中的行政区'雁塔区'与Excel风险点列表中的雁塔区匹配，因此可以确定匹配。,"['主要街道', '行政区']",176,雁塔区,锦业路,"路口:锦业路与丈八七路,方向:东北,距离:126.411m",108.838099,34.196032
False,,,0.0,经过对比，CSV中的积水点位于丈八六路辅路与南三环交汇处，而Excel标准风险点列表中并没有直接匹配的道路名称或交叉口描述。虽然丈八六路是丈八街道的一部分，但Excel风险点列表中并没有提到丈八六路或其附近的交叉口。因此，无法直接匹配。,[],178,雁塔区,丈八六路,"路口:丈八六路辅路与南三环,方向:西北,距离:146.488m",108.850229,34.188584
True,20.0,XA128,1.0,CSV中的积水点位于沈家桥三路与西沣四路十字，与Excel标准风险点列表中的编号为XA128的风险点描述完全一致。,"['沈家桥三路与西沣四路十字', 'XA128']",201,雁塔区,西沣四路,"路口:西沣四路与沈家桥三路,方向:东北,距离:178.563m",108.883052,34.177703
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。具体分析如下：
1. 街道名称匹配：CSV中的主要街道为锦业路，而Excel风险点列表中没有完全匹配的街道名称。
2. 路口匹配：CSV中的路口详情为丈八六路与锦业路交汇，Excel风险点列表中没有完全匹配的路口描述。
3. 关键词匹配：CSV中的路口描述中没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，而Excel风险点列表中有些记录包含这些关键词。
4. 地理位置匹配：CSV中的行政区为雁塔区，与Excel风险点列表中的区域匹配，但不足以确定具体匹配的风险点。
5. 精确匹配优先：由于没有完全匹配的记录，因此无法确定匹配的Excel记录索引和编号。",[],175,雁塔区,锦业路,"路口:丈八六路与锦业路,方向:西,距离:324.01m",108.848534,34.195809
True,55.0,XA297,1.0,CSV中的积水点位于雁塔区杜城街道西沣二路，与Excel中编号为XA297的风险点描述'西沣三路'完全匹配，且风险等级为蓝色，符合精确匹配优先的策略。,"['雁塔区', '杜城街道', '西沣二路', '西沣三路']",203,雁塔区,西沣二路,"路口:西沣三路与沈家桥三路,方向:东北,距离:227.345m",108.885371,34.181627
False,,,0.0,经过对比，CSV中的积水点位于雁塔区杜城街道雁环中路与三义巷交叉口，但该信息在Excel标准风险点列表中并未找到完全匹配的记录。虽然雁环中路在列表中存在，但交叉口信息（雁环中路与三义巷）以及具体的方向和距离信息在列表中并未提及。因此，无法确定是否存在匹配的风险点。,"['雁塔区', '杜城街道', '雁环中路', '三义巷']",211,雁塔区,雁环中路,"路口:雁环中路与三义巷,方向:东,距离:199.497m",108.920334,34.185016
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为雁环中路，而Excel列表中的风险点名称没有直接包含这一信息。虽然丈杜路与雁环中路的交叉口信息在CSV中提及，但在Excel列表中没有找到直接对应的交叉口描述。因此，无法确定匹配。,[],212,雁塔区,雁环中路,"路口:丈杜路与雁环中路,方向:东,距离:236.386m",108.910523,34.186265
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为电子正街，而Excel风险点列表中没有直接匹配的街道名称。虽然CSV中提到了双桥一巷与东盛巷的交叉口，但在Excel风险点列表中并没有找到与之完全匹配的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],214,雁塔区,电子正街,"路口:双桥一巷与东盛巷,方向:东南,距离:96.199m",108.915517,34.196255
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的主要街道为子午大道，而Excel风险点列表中并未包含此街道。虽然CSV中的路口详情提到了杜城西路与南三环的交叉口，但Excel风险点列表中并未提及此交叉口。因此，无法找到直接匹配的风险点。,[],213,雁塔区,子午大道,"路口:杜城西路与南三环,方向:东南,距离:47.5864m",108.904101,34.190368
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为朱雀大街南段，而Excel列表中的风险点没有直接提及此街道。虽然路口详情中提到了秦安路与朱雀大街南段的交叉口，但Excel列表中没有对应的交叉口描述。因此，无法确定匹配。,[],215,雁塔区,朱雀大街南段,"路口:秦安路与朱雀大街南段,方向:东,距离:83.3271m",108.93514,34.196255
True,2.0,XA299,0.9,CSV中的积水点位于雁塔区曲江街道公田一路，与Excel标准风险点列表中的编号为XA299的风险点（西三环丈八立交）在街道名称上存在部分匹配，且Excel风险点描述中包含'立交'这一关键词，与CSV中的'丈八立交'存在关联。虽然不完全匹配，但考虑到地理位置和关键词的相似性，可以认为存在较高匹配度。,"['雁塔区', '曲江街道', '公田一路', '丈八立交']",220,雁塔区,公田一路,"路口:公田一路与金滹沱一路,方向:东南,距离:155.71m",108.971753,34.183768
True,12.0,XA153,1.0,CSV中的路口详情明确指出路口为雁南四路与庙坡头路，这与Excel风险点列表中编号为XA153的风险点描述‘雁南四路与庙坡头路十字’完全一致。,"['雁南四路与庙坡头路', '十字']",221,雁塔区,庙坡头路,"路口:雁南四路与庙坡头路,方向:东北,距离:121.964m",108.965866,34.199822
True,25.0,XA79,1.0,CSV中的积水点位于雁塔区慈恩西路，与Excel标准风险点列表中编号为XA79的风险点完全匹配，该风险点描述为慈恩西路，位于雁塔区。,"['雁塔区', '慈恩西路']",222,雁塔区,雁塔南路,"路口:雁南二路与慈恩西路,方向:东北,距离:100.627m",108.957125,34.207314
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为曲江路，而Excel列表中没有直接匹配的街道名称。虽然CSV中提到了北池头一路与曲江路辅路的交叉口，但Excel列表中没有包含这样的交叉口描述。因此，无法确定具体的匹配记录。,[],225,雁塔区,曲江路,"路口:北池头一路与曲江路辅路,方向:东北,距离:178.951m",108.978353,34.222299
True,17.0,XA78,1.0,CSV中的主要街道'芙蓉东路'与Excel风险点列表中编号为XA78的风险点'芙蓉东路'完全匹配，且行政区'雁塔区'也一致，因此可以确定匹配。,"['主要街道', '行政区']",224,雁塔区,芙蓉东路,"路口:北池头一路与曲江路辅路,方向:西南,距离:265.737m",108.976212,34.218731
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为慈恩路，而Excel列表中的风险点没有直接提及慈恩路。虽然路口详情中提到了雁塔东步行街与慈恩路的交叉口，但Excel列表中没有对应的交叉口描述。因此，无法确定CSV中的积水点与Excel中的风险点有直接关联。,[],223,雁塔区,慈恩路,"路口:雁塔东步行街与慈恩路,方向:西,距离:51.8443m",108.959979,34.218017
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为上巳路，而Excel列表中的风险点均未提及上巳路。虽然CSV中的路口详情提到了博学路与鸿鹄路，但在Excel列表中也没有相应的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],227,雁塔区,上巳路,"路口:博学路与鸿鹄路,方向:西南,距离:140.703m",108.991375,34.220693
True,1.0,XA83,0.95,CSV中的积水点位于雁塔区曲江街道曲江路，且路口详情提到北池头二路与北池头二路立交，这与Excel中编号为XA83的风险点'北池头二路'完全匹配，且风险等级为红色。,"['雁塔区', '曲江街道', '曲江路', '北池头二路立交']",226,雁塔区,曲江路,"路口:北池头二路与北池头二路立交,方向:西南,距离:89.0505m",108.982456,34.218374
True,22.0,XA230,1.0,CSV中的积水点位于电子一路与永松路交汇以西，这与Excel中编号为XA230的风险点描述完全一致。,"['电子一路', '永松路', '交汇以西']",235,雁塔区,电子一路,"路口:永松路与电子一路,方向:西,距离:235.027m",108.912752,34.217393
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为咸宁东路，而Excel列表中虽然有咸宁东路恒大绿洲的风险点（编号XA38），但该风险点并未包含CSV中提到的具体路口信息（咸宁东路与咸史路交叉口）。因此，无法确定CSV中的积水点与Excel中的哪个风险点完全匹配。,[],229,雁塔区,咸宁东路,"路口:咸宁东路与咸史路,方向:东,距离:174.307m",109.039583,34.252178
False,,,0.0,经过对比分析，CSV中的积水点位于昆明路，但Excel标准风险点列表中没有直接匹配的街道名称。虽然存在编号为XA150的风险点，描述为'丈八北与昆明路十字'，但CSV中的积水点位于昆明路与汉城南路交叉口，而非昆明路本身，因此无法直接匹配。,['昆明路'],277,雁塔区,昆明路,"路口:昆明路与汉城南路,方向:南,距离:58.8551m",108.867599,34.253722
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为二环南路西段，而Excel风险点列表中没有直接提及此街道。虽然CSV中的路口详情提到了二环南路西段辅路与高新一路的交叉口，但Excel风险点列表中没有对应的交叉口描述。因此，无法确定CSV积水点与Excel风险点之间的匹配关系。,[],274,雁塔区,二环南路西段,"路口:二环南路西段辅路与高新一路,方向:西北,距离:15.266m",108.902696,34.24177
False,,,0.0,CSV中的积水点位于后村西路与乐游路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然雁塔区作为行政区存在匹配，但街道名称和路口详情没有直接对应的风险点。,"['雁塔区', '后村西路', '乐游路']",282,雁塔区,后村西路,"路口:后村西路与乐游路,方向:东南,距离:59.5871m",108.963413,34.227589
True,5.0,XA37,0.95,CSV中的积水点位于西影路，与Excel中编号为XA37的风险点'西影路阳光小区'的街道名称完全匹配，且没有其他更精确的匹配项。,"['街道名称：西影路', '风险点：西影路阳光小区']",281,雁塔区,西影路,"路口:乐游东路与乐游路东段,方向:东,距离:38.5684m",108.97066,34.226942
True,37.0,XA225,1.0,CSV中的主要街道'唐延南路'与Excel风险点编号XA225的描述'唐延南路与锦业路交汇以北'完全匹配，且路口详情中提到的'锦业路与西沣辅道'与Excel风险点编号XA225的描述相符，因此可以确定匹配。,"['唐延南路', '锦业路', '交汇', '西北', '210.363m']",287,雁塔区,唐延南路,"路口:锦业路与西沣辅道,方向:西北,距离:210.363m",108.88624,34.197174
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为丈八八路，而Excel列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了云水一路与天谷九路，但Excel列表中没有对应的交叉口描述。此外，CSV中的关键词如'隧道'、'下穿'、'立交'、'桥'等在Excel列表中也没有对应的风险点。因此，无法确定匹配。,[],290,雁塔区,丈八八路,"路口:云水一路与天谷九路,方向:东北,距离:51.5602m",108.832502,34.206138
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为科技西路，但Excel列表中没有直接匹配的街道名称。虽然CSV中提到了富源路与富源四路交叉口，但Excel列表中没有包含这个交叉口的描述。因此，无法确定具体的匹配记录。,[],241,雁塔区,科技西路,"路口:富源路与富源四路,方向:东南,距离:76.472m",108.817719,34.246068
True,6.0,XA94,1.0,CSV中的积水点位于灞桥区，主要街道为长十路，这与Excel标准风险点列表中编号为XA94的风险点（长十路）完全匹配。,"['行政区：灞桥区', '主要街道：长十路']",82,灞桥区,长十路,"路口:长乐东路与长十路,方向:西北,距离:328.839m",109.024424,34.276218
True,28.0,XA92,1.0,CSV中的主要街道'长荣北路'与Excel风险点列表中编号为XA92的风险点'长荣北路'完全匹配，且CSV中的行政区'灞桥区'与Excel风险点列表中的灞桥区相符，因此可以确定匹配。,"['长荣北路', '灞桥区']",83,灞桥区,长荣北路,"路口:长乐东路与长荣北路,方向:南,距离:81.8742m",109.026222,34.272404
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的主要街道为华清东路，而Excel风险点列表中并未包含华清东路的相关记录。虽然CSV中的路口详情提到了华清东路与公园北路交叉口，但Excel风险点列表中并未提及此交叉口。因此，无法找到直接匹配的风险点。,[],84,灞桥区,华清东路,"路口:华清东路与公园北路,方向:东南,距离:49.0604m",109.001585,34.289018
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于金桥三路与金茂七路交叉口，而Excel风险点列表中没有包含这个具体的交叉口信息。尽管CSV中的积水点位于灞桥区，但并没有找到与之精确匹配的风险点。,[],89,灞桥区,金桥三路,"路口:金桥三路与金茂七路,方向:西,距离:207.837m",109.03626,34.306422
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的积水点位于灞桥区席王街道金茂五路东，而Excel风险点列表中的灞桥区风险点主要集中在不同的道路和交叉口，没有与金茂五路直接相关的风险点。此外，CSV中的路口详情为东三环辅路与金桥二路交叉口，而Excel风险点列表中没有提及此交叉口。因此，无法找到匹配的风险点。,[],91,灞桥区,金茂五路,"路口:东三环辅路与金桥二路,方向:东北,距离:98.1618m",109.044063,34.305545
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。具体分析如下：
1. CSV中的街道名称'御锦四路'与Excel风险点列表中的任何道路名称不完全匹配。
2. CSV中的路口详情'路口:浐河东路与西安高架快速干道辅路,方向:西,距离:186.494m'与Excel风险点列表中的任何交叉口描述不匹配。
3. CSV中的关键词'隧道'、'下穿'、'立交'、'桥'等在Excel风险点列表中未找到直接匹配项。
4. CSV中的行政区'灞桥区'与Excel风险点列表中的灞桥区相符，但不足以确定匹配，因为灞桥区有多个风险点。
5. 由于没有直接匹配的记录，因此无法给出精确匹配的置信度。
",[],86,灞桥区,御锦四路,"路口:浐河东路与西安高架快速干道辅路,方向:西,距离:186.494m",109.029597,34.290333
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为金融路，而Excel风险点列表中没有直接提及金融路。虽然CSV中的路口详情提到了东三环辅路与广安路的交叉口，但Excel风险点列表中没有直接匹配的交叉口描述。此外，CSV中的积水点位于灞桥区席王街道，但Excel风险点列表中没有直接匹配的街道名称。,[],90,灞桥区,金融路,"路口:东三环辅路与广安路,方向:东,距离:94.0566m",109.041871,34.312384
True,51.0,XA50,1.0,CSV中的主要街道为电厂西路，与Excel风险点列表中的编号XA50对应的风险点描述'电厂西路'完全匹配，且Excel风险点列表中的编号XA50的风险等级为橙色，与CSV中的风险等级描述相符。,['电厂西路'],92,灞桥区,电厂西路,"路口:电厂西路与原华清路,方向:南,距离:43.5785m",109.050814,34.294147
True,12.0,XA147,0.9,CSV中的积水点位于灞桥区的席王街道，主要街道为柳虹路，且路口详情提到灞瑞二路与柳虹路交汇，这与Excel中编号为XA147的风险点描述'灞瑞二路与柳路交汇'高度匹配，尽管Excel中描述的柳路可能是指柳虹路，因此匹配的置信度较高。,"['灞桥区', '席王街道', '柳虹路', '灞瑞二路与柳虹路交汇']",93,灞桥区,柳虹路,"路口:灞瑞二路与柳虹路,方向:东南,距离:81.2092m",109.059056,34.296602
True,38.0,XA48,1.0,CSV中的主要街道'席王路'与Excel风险点中的'席王路中段'完全匹配，且CSV中的行政区'灞桥区'与Excel风险点所在的区域一致，因此可以确定匹配。,"['席王路', '灞桥区']",94,灞桥区,席王路,"路口:席王路与纺渭路,方向:西,距离:138.162m",109.066596,34.288185
True,14.0,XA137,1.0,CSV中的积水点位于西柳瑞路与灞瑞一路十字，这与Excel中编号为XA137的风险点描述'西柳瑞路与灞瑞一路十字'完全匹配。,"['西柳瑞路', '灞瑞一路', '十字']",95,灞桥区,纺渭路,"路口:西柳瑞路与灞瑞一路,方向:东,距离:50.7706m",109.07247,34.295024
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于灞桥区灞桥街道东城大道西北方向，路口为东城大道辅路与泽柳路交汇处，但Excel风险点列表中没有包含这些具体信息。虽然泽柳路与东城大道交汇处有风险点编号为XA101，但该风险点位于泽柳路与东城大道交汇往南，与积水点位置不符。,[],97,灞桥区,东城大道,"路口:东城大道辅路与泽柳路,方向:东南,距离:518.001m",109.077994,34.308789
True,3.0,XA45,0.95,CSV中的积水点位于灞桥区的纺渭路，且路口详情提到S3011绕城高速联络线出口与柳烟路交汇，这与Excel中编号为XA45的风险点描述'西临高速纺渭路下穿'高度匹配，考虑到关键词'下穿'和地理位置的匹配，因此匹配置信度较高。,"['灞桥区', '纺渭路', '下穿']",96,灞桥区,纺渭路,"路口:S3011绕城高速联络线出口与柳烟路,方向:东南,距离:135.963m",109.069752,34.301337
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为柳雪路，而Excel风险点列表中没有直接提及柳雪路。虽然CSV中提到了柳莺路，但Excel风险点列表中也没有直接提及柳莺路作为交叉口。因此，没有找到完全匹配的风险点。,[],99,灞桥区,柳雪路,"路口:柳雪路与柳莺路,方向:东北,距离:597.041m",109.090443,34.292131
True,3.0,XA45,0.9,CSV中的积水点位于灞桥区纺渭路，与Excel中编号为XA45的风险点描述'西临高速纺渭路下穿'中的'纺渭路'和'下穿'关键词完全匹配，且行政区和街道信息也一致，因此具有较高的匹配置信度。,"['灞桥区', '纺渭路', '下穿']",100,灞桥区,纺渭路,"路口:077村道与新兴庄路,方向:南,距离:752.335m",109.082553,34.329831
True,24.0,XA120,0.9,CSV中的积水点位于灞桥区席王街道柳雪路与柳莺路交叉口，距离69.0568米，这与Excel中编号为XA120的风险点描述‘柳莺路和柳雪路交汇’高度匹配，且位于同一街道，因此匹配度较高。,"['灞桥区', '席王街道', '柳雪路', '柳莺路', '交汇']",98,灞桥区,柳雪路,"路口:柳雪路与柳莺路,方向:东,距离:69.0568m",109.088339,34.287046
True,11.0,XA257,0.95,CSV中的积水点位于灞桥区，街道为灞桥街道，主要街道为世博大道，这与Excel中编号为XA257的风险点描述'香槐路与世博大道交汇（下穿）'高度匹配。虽然CSV中没有提到'香槐路'，但考虑到世博大道是主要街道，且路口详情中提到'路口:世博大道与后堡路'，可以推断出两者是同一地点。,"['灞桥区', '世博大道', '灞桥街道', '路口:世博大道与后堡路']",101,灞桥区,世博大道,"路口:世博大道与后堡路,方向:北,距离:151.549m",109.061511,34.330182
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为世博东路，而Excel风险点列表中没有直接提及此街道。虽然CSV中提到了世博中路与228乡道的交叉口，但Excel风险点列表中没有包含此交叉口的具体描述。因此，无法确定CSV积水点与Excel风险点之间的匹配关系。,[],103,灞桥区,世博东路,"路口:世博中路与228乡道,方向:南,距离:290.178m",109.052042,34.338248
True,39.0,XA282,1.0,CSV中的积水点位于灞桥区，街道为灞桥街道，主要街道为世博东路，路口详情为世博大道与香湖湾五路交汇，这与Excel中编号为XA282的风险点描述完全一致。,"['灞桥区', '灞桥街道', '世博东路', '世博大道与香湖湾五路交汇']",102,灞桥区,世博东路,"路口:世博大道与香湖湾五路,方向:东,距离:93.9232m",109.055724,34.335267
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于灞桥区欧亚大道与世博大道交汇处，而Excel列表中的风险点主要集中在不同的道路和交叉口，没有直接匹配的记录。,[],105,灞桥区,欧亚大道,"路口:欧亚大道与世博大道,方向:西北,距离:106.075m",109.030824,34.347717
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为欧亚大道，与Excel风险点列表中的欧亚大道相关记录进行了对比，但路口详情和具体位置描述不匹配。例如，CSV中的路口为欧亚大道与会展一路，而Excel风险点列表中没有直接提及此交叉口。因此，无法确定匹配的风险点。,[],106,灞桥区,欧亚大道,"路口:欧亚大道与会展一路,方向:东北,距离:156.591m",109.025564,34.342631
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为世博东路，而Excel风险点列表中没有直接提及此街道。虽然CSV中的路口详情提到了奥莱西路与西会展路，但在Excel风险点列表中并未找到与此完全对应的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],104,灞桥区,世博东路,"路口:奥莱西路与西会展路,方向:东南,距离:138.183m",109.038891,34.34456
True,11.0,XA257,0.95,CSV中的积水点位于灞桥区，街道为灞桥街道，主要街道为世博大道，这与Excel中编号为XA257的风险点描述'香槐路与世博大道交汇（下穿）'高度匹配。虽然CSV中的具体路口与Excel描述不完全一致，但考虑到'世博大道'这一关键词的匹配，以及'下穿'这一特殊描述，可以认为这是一个高置信度的匹配。,"['灞桥区', '灞桥街道', '世博大道', '下穿']",107,灞桥区,世博大道,"路口:锦堤五路与展业路,方向:东,距离:98.0844m",109.025739,34.349821
True,16.0,XA47,1.0,CSV中的主要街道为向阳北路，与Excel风险点列表中编号为XA47的风险点描述'向阳北路'完全匹配，且CSV中的行政区为灞桥区，与风险点所在的区域一致。,['向阳北路'],110,灞桥区,向阳北路,"路口:向阳中路西段与宇航西路,方向:东北,距离:384.633m",109.117272,34.308263
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为华文路，而Excel风险点列表中没有直接提及华文路。虽然CSV中提到了世博大道与欧亚六路辅路的交叉口，但在Excel风险点列表中也没有直接对应的交叉口描述。因此，无法确定CSV积水点与Excel风险点之间的匹配关系。,[],108,灞桥区,华文路,"路口:世博大道与欧亚六路辅路,方向:东北,距离:46.7141m",109.017147,34.356484
True,8.0,XA138,0.9,CSV中的积水点位于灞桥区洪庆街道灞柳一路，与Excel标准风险点列表中的编号为XA138的风险点'灞柳一路与纺院园二路十字'在街道名称和路口详情上存在直接匹配，且Excel风险点描述中包含'灞柳一路'，因此匹配度较高。,"['灞桥区', '洪庆街道', '灞柳一路', '灞柳一路与纺院园二路十字']",109,灞桥区,灞柳一路,"路口:灞柳一路与纺园二路,方向:东南,距离:41.0784m",109.104822,34.281084
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的主要街道为东二路，而Excel风险点列表中没有包含东二路的相关记录。虽然CSV中提到了洪威路与洪朗路交叉口，但Excel风险点列表中没有直接提及这一交叉口。因此，无法找到完全匹配的记录。,[],111,灞桥区,东二路,"路口:洪威路与洪朗路,方向:东南,距离:174.746m",109.116395,34.326675
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不完全匹配。CSV中的积水点位于灞桥区新筑街道于家东路，路口为港兴一路与元朔大道交汇处，但Excel列表中的风险点没有直接提及这些具体信息。虽然元朔大道在Excel列表中多次出现，但与积水点位置不完全对应。,[],112,灞桥区,于家东路,"路口:港兴一路与元朔大道,方向:西,距离:421.898m",109.070804,34.35929
True,27.0,XA27,0.9,CSV中的积水点位于灞桥区东三环，与Excel标准风险点列表中的编号为XA27的风险点（东三环路）完全匹配，且Excel风险点列表中的描述包含'东三环路'这一关键词，因此匹配度较高。,"['灞桥区', '东三环']",113,灞桥区,东三环,"路口:港兴路与新筑路,方向:东北,距离:240.594m",109.051954,34.358238
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的积水点位于灞桥区新筑街道港兴路，而Excel风险点列表中并未包含该街道或与之相关的交叉口描述。尽管CSV中的路口详情提到了和畅路与元朔大道的交叉口，但Excel风险点列表中并未提及这一交叉口。因此，无法找到匹配的风险点。,[],114,灞桥区,港兴路,"路口:和畅路与元朔大道,方向:西北,距离:66.6496m",109.042573,34.362972
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。虽然CSV中的积水点位于灞桥区，且主要街道为港兴路，但Excel列表中没有直接匹配的街道名称或路口详情。CSV中的路口详情提到了欧亚大道辅路与元朔大道的交汇处，但Excel列表中没有提及这一具体交叉口。,[],115,灞桥区,港兴路,"路口:欧亚大道辅路与元朔大道,方向:南,距离:238.268m",109.034507,34.361306
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均无直接匹配。CSV中的积水点位于灞桥区新筑街道向东路，而Excel风险点列表中的灞桥区风险点主要集中在不同的道路和交叉口，没有与向东路直接相关的风险点。虽然CSV中的欧亚大道辅路与港泽路交叉口与Excel中的一些风险点存在部分重合，但缺乏精确匹配的关键要素，如'隧道'、'下穿'、'立交'、'桥'等，因此无法确定匹配。,[],116,灞桥区,向东路,"路口:欧亚大道辅路与港泽路,方向:东,距离:146.457m",109.036699,34.369635
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为杏渭路，而Excel风险点列表中没有直接提及杏渭路。虽然CSV中的路口详情提到了全运路辅路与港兴二路辅路，但在Excel风险点列表中并没有找到与之完全对应的交叉口描述。因此，无法确定积水点与风险点的匹配关系。,[],117,灞桥区,杏渭路,"路口:全运路辅路与港兴二路辅路,方向:东,距离:103.742m",109.020128,34.374457
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的积水点位于灞桥区新筑街道，路口为港兴二路辅路与灞渭大道交汇处，但Excel列表中的风险点没有直接提及这些具体信息。虽然灞渭大道在Excel列表中出现过，但其他信息如新筑街道和港兴二路辅路并未直接对应。,[],118,灞桥区,[],"路口:港兴二路辅路与灞渭大道,方向:东北,距离:205.501m",109.016446,34.375772
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均无直接匹配。CSV中的路口详情为港兴三路与灞渭大道，而Excel风险点列表中并没有直接提及这两个道路的交叉口。虽然灞渭大道在列表中出现过，但与CSV中的积水点位置不符。,[],119,灞桥区,[],"路口:港兴三路与灞渭大道,方向:东南,距离:217.3m",109.014955,34.37963
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于灞桥区灞桥街道港务大道与港兴路的东北方向，距离22.8332米，而Excel风险点列表中的记录没有包含这样的具体路口描述和方向信息。,[],125,灞桥区,港务大道,"路口:港务大道与港兴路,方向:东北,距离:22.8332m",109.059143,34.356659
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于灞桥区新筑街道港务大道，而Excel列表中的风险点主要集中在不同的道路和交叉口，没有与港务大道直接相关的风险点。虽然CSV中的路口详情提到了贤德巷与汇通路的交叉口，但Excel列表中没有提及这两个道路的交叉口。因此，无法确定CSV中的积水点与Excel中的任何风险点匹配。,[],126,灞桥区,港务大道,"路口:贤德巷与汇通路,方向:西南,距离:244.393m",109.067999,34.376737
True,4.0,XA85,0.95,CSV中的积水点位于灞桥区纺渭路，与Excel标准风险点列表中的编号为XA85的风险点'纺渭路下穿铁路北环线'完全匹配，且该风险点描述中包含'下穿'关键词，符合匹配策略。,"['灞桥区', '纺渭路', '下穿']",127,灞桥区,[],"路口:纺渭路与保税六路,方向:东,距离:509.808m",109.088164,34.385329
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的积水点位于灞桥区新筑街道亚洲道，而Excel列表中的风险点主要集中在不同的道路和交叉口，没有与亚洲道直接相关的风险点。尽管CSV中的路口详情提到了秦汉大道与港浦路的交叉口，但Excel列表中没有直接提及这一交叉口。因此，无法确定CSV中的积水点与Excel中的任何风险点有直接关联。,[],128,灞桥区,亚洲道,"路口:秦汉大道与港浦路,方向:北,距离:162.328m",109.069752,34.402513
True,3.0,XA45,0.95,CSV中的积水点位于灞桥区纺渭路，与Excel标准风险点列表中的编号为XA45的风险点描述'西临高速纺渭路下穿'高度匹配，其中'纺渭路'和'下穿'是关键匹配要素。,"['灞桥区', '纺渭路', '下穿']",129,灞桥区,纺渭路,"路口:港通路与纺渭路,方向:东南,距离:430.876m",109.078169,34.410404
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于秦汉大道与新筑路交叉口，而Excel风险点列表中并没有直接提及这一交叉口或与之相关的描述。尽管存在一些关键词如'秦汉大道'，但它们并不足以构成精确匹配。,"['秦汉大道', '新筑路']",130,灞桥区,秦汉大道,"路口:秦汉大道与新筑路,方向:西北,距离:200.713m",109.052919,34.400234
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于灞桥区新筑街道欧亚大道辅路与港安路的路口，而Excel风险点列表中并没有直接提及这个具体的路口或街道。尽管存在一些关键词如'欧亚大道'和'港安路'，但它们并不足以匹配列表中的任何风险点。,"['灞桥区', '新筑街道', '欧亚大道辅路', '港安路']",131,灞桥区,[],"路口:欧亚大道辅路与港安路,方向:东北,距离:211.932m",109.039767,34.391116
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的积水点位于灞桥区新合街道鹤翔路东北方向，路口为灞耿路与欧亚大道交汇处，但Excel列表中的风险点没有直接提及这些具体信息。虽然CSV中的欧亚大道与Excel列表中的欧亚大道有重合，但其他信息如街道名称、路口详情等均不匹配。,[],133,灞桥区,鹤翔路,"路口:灞耿路与欧亚大道,方向:东南,距离:512.117m",109.037838,34.413736
True,5.0,XA88,0.95,CSV中的积水点位于灞桥区灞渭大道，与Excel标准风险点列表中的编号为XA88的风险点'灞渭大道下穿隧道'完全匹配，且灞渭大道下穿隧道与灞渭大道的描述一致，因此匹配度高。,"['灞桥区', '灞渭大道', '下穿隧道']",134,灞桥区,灞渭大道,"路口:扬帆路与全运路,方向:东,距离:95.7319m",109.022583,34.394447
True,5.0,XA88,0.95,CSV中的积水点位于灞桥区灞渭大道，与Excel标准风险点列表中的编号为XA88的风险点'灞渭大道下穿隧道'完全匹配，且灞渭大道下穿隧道是一个具体的地点描述，因此匹配度较高。,"['灞桥区', '灞渭大道', '下穿隧道']",135,灞桥区,灞渭大道,"路口:扬帆路与港安路,方向:东北,距离:209.337m",109.019427,34.39357
False,,,0.0,"经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不完全匹配。具体分析如下：
1. 街道名称匹配：CSV中的主要街道为杏渭路，而Excel风险点列表中没有完全匹配的街道名称。
2. 路口匹配：CSV中的路口详情为港安路与奥体大道交汇，而Excel风险点列表中没有完全匹配的路口描述。
3. 关键词匹配：CSV中的路口描述包含'路口'和'方向'，但没有包含'隧道'、'下穿'、'立交'、'桥'等关键词。
4. 地理位置匹配：CSV中的行政区为灞桥区，与Excel风险点列表中的灞桥区匹配，但不足以确定具体匹配的风险点。
5. 精确匹配优先：由于没有完全匹配的记录，因此无法确定精确匹配的风险点。",[],132,灞桥区,杏渭路,"路口:港安路与奥体大道,方向:东,距离:211.728m",109.031877,34.391291
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为纺西街，而Excel风险点列表中没有直接提及纺西街。路口详情为纺织城西街与纺四路，但Excel风险点列表中没有提及这两个街道的交叉口。因此，无法确定积水点与风险点的匹配关系。,[],231,灞桥区,纺西街,"路口:纺织城西街与纺四路,方向:西,距离:109.664m",109.054032,34.264665
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于浐河西路与韩森东路交叉口西南方向，距离97.3929米，而Excel风险点列表中的记录没有提及这一具体的交叉口和距离信息。尽管韩森东路是列表中某些风险点的道路名称，但交叉口和具体位置描述不匹配。,[],230,灞桥区,韩森东路,"路口:浐河西路与韩森东路,方向:西南,距离:97.3929m",109.037799,34.26074
True,10.0,XA107,0.95,"CSV中的主要街道'半引路'与Excel风险点中的'东三环路'存在关联，且CSV中的路口详情'路口:东三环辅路与云霞巷,方向:东南,距离:48.3757m'与Excel风险点中的'东三环路与云霞巷交汇'完全匹配，同时'东三环路'和'云霞巷'在Excel风险点中均有提及，因此匹配度较高。","['半引路', '东三环路', '云霞巷', '路口', '东南', '东三环路与云霞巷交汇']",228,灞桥区,半引路,"路口:东三环辅路与云霞巷,方向:东南,距离:48.3757m",109.049394,34.245399
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为半坡路，而Excel风险点列表中没有直接提及半坡路。路口详情中的郭家滩路与半坡路交叉口在Excel风险点列表中也没有直接对应。因此，无法确定积水点与风险点的匹配关系。,[],232,灞桥区,半坡路,"路口:郭家滩路与半坡路,方向:西北,距离:397.666m",109.042616,34.268768
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的积水点位于灞桥区新合街道欧亚大道辅路与秦汉大道辅路交叉口，而Excel列表中的风险点均未提及此交叉口或街道。尽管存在“欧亚大道”这一关键词，但其他信息如“秦汉大道辅路”和“新合街道”在风险点列表中并未出现。,[],283,灞桥区,[],"路口:欧亚大道辅路与秦汉大道辅路,方向:西北,距离:167.124m",109.037732,34.397633
True,46.0,XA260,0.9,CSV中的积水点位于灞桥区席王街道柳新路，与Excel中编号为XA260的风险点描述‘港安路与欧亚大道交汇以北’有较高的匹配度。虽然CSV中没有明确提到‘港安路’和‘欧亚大道’，但考虑到柳新路与港安路、欧亚大道的地理位置关系，可以推断出两者之间存在关联。,"['灞桥区', '席王街道', '柳新路', '港安路', '欧亚大道']",236,灞桥区,柳新路,"路口:东柳瑞路与柳新路,方向:西,距离:80.5422m",109.075304,34.291556
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于郭杜街道雷甘路，而Excel风险点列表中的街道名称和路口描述与CSV中的信息不匹配。此外，CSV中的路口详情提到了西沣路与纬三十路，但在Excel风险点列表中没有直接对应的交叉口描述。,[],180,长安区,雷甘路,"路口:西沣路与纬三十路,方向:东南,距离:407.635m",108.850943,34.138458
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为雷甘路，而Excel风险点列表中没有雷甘路的相关记录。虽然CSV中的路口详情提到了仓台西路与香积大街的交叉口，但Excel风险点列表中没有提及这一交叉口。因此，无法确定积水点与风险点的匹配关系。,[],179,长安区,雷甘路,"路口:仓台西路与香积大街,方向:东北,距离:57.8875m",108.854867,34.132572
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的主要街道为丈八七路，而Excel中的风险点列表中没有直接提及丈八七路。虽然CSV中提到了丈八七路与绕城高速北辅道的交叉口，但Excel中的风险点列表中没有提及绕城高速北辅道或其交叉口。因此，没有找到完全匹配的记录。,[],177,长安区,丈八七路,"路口:丈八七路与绕城高速北辅道,方向:东南,距离:127.735m",108.838099,34.188227
False,,,0.0,CSV中的积水点位于建业三路与顺兴路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称和交叉口描述。虽然建业三路在长安区，但其他匹配条件（如路口详情、关键词、地理位置）均不满足。,[],181,长安区,建业三路,"路口:建业三路与顺兴路,方向:西南,距离:153.178m",108.859862,34.152907
False,,,0.0,CSV中的积水点位于文苑中路与建业三路交汇处，而Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然文苑中路与建业三路在长安区，但CSV中的积水点并未与Excel中的任何风险点精确匹配。,[],182,长安区,文苑中路,"路口:建业三路与文苑中路,方向:东南,距离:219.284m",108.876987,34.152729
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录均不匹配。CSV中的街道名称为'郭杜西街'，而Excel风险点列表中没有完全匹配的街道名称。路口详情中提到的'纬二十四路与规划十二路'交叉口在Excel风险点列表中也没有直接匹配的描述。因此，无法确定积水点与风险点的匹配关系。,[],184,长安区,郭杜西街,"路口:纬二十四路与规划十二路,方向:北,距离:196.056m",108.843629,34.156475
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的街道为'郭杜街道'，主要街道为'西太公路'，路口详情为'西太路与纬三十二路交叉口，方向东北，距离302.037m'，这些信息在Excel风险点列表中没有直接对应的记录。虽然Excel中有一个风险点'西太路与香积大道交汇以北南'（编号XA187），但该风险点与CSV中的积水点位置和街道不完全一致。,[],183,长安区,西太公路,"路口:西太路与纬三十二路,方向:东北,距离:302.037m",108.832747,34.144702
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的积水点位于经十九路与纬二十四路的交叉口，而Excel中的风险点列表并未包含这一具体交叉口。尽管CSV中的行政区为长安区，但街道名称和路口详情与Excel中的风险点描述不一致。,[],185,长安区,经十九路,"路口:经十九路与纬二十四路,方向:西北,距离:59.6947m",108.838277,34.156653
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于纬二十六路与经三十路的交叉口，而Excel列表中的风险点主要集中在长安区的主要道路交叉口，没有与纬二十六路和经三十路直接相关的风险点。,[],187,长安区,纬二十六路,"路口:纬二十六路与经三十路,方向:东北,距离:348.263m",108.820439,34.159151
True,13.0,XA272,1.0,CSV中的主要街道'上林苑三路'与Excel风险点中的'上林宛三路'完全匹配，且CSV中的路口详情'毕原三路西段与上林苑三路'与Excel风险点中的'毕原三路西段与上林宛三路交汇'完全匹配，同时CSV中的行政区'长安区'与Excel风险点所在的区域一致。,"['上林苑三路', '毕原三路西段与上林苑三路', '长安区']",188,长安区,上林苑三路,"路口:毕原三路西段与上林苑三路,方向:东北,距离:241.175m",108.820261,34.165929
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道为'郭杜街道'，而Excel风险点列表中没有直接匹配的街道名称。CSV中的路口详情为'309县道与经二十路，方向：东，距离：209.519m'，这与Excel风险点列表中的任何交叉口描述都不相符。此外，CSV中的行政区为'长安区'，但Excel风险点列表中的风险点并未具体到街道级别，因此无法通过行政区进行匹配。,[],186,长安区,郭杜西街,"路口:309县道与经二十路,方向:东,距离:209.519m",108.838277,34.162719
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于上林苑三路，而Excel风险点列表中没有直接提及上林苑三路。虽然CSV中提到了毕原一路与上林苑三路的交叉口，但Excel风险点列表中没有提及该交叉口。因此，无法确定CSV中的积水点与Excel风险点列表中的某个风险点有直接关联。,[],189,长安区,上林苑三路,"路口:毕原一路与上林苑三路,方向:西南,距离:125.674m",108.817406,34.169675
False,,,0.0,经过对比分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为西部大道，但Excel中的风险点列表中并没有直接提到西部大道与毕原一路西段与上林苑五路的交叉口。虽然CSV中的路口详情提到了毕原一路西段与上林苑五路，但Excel风险点列表中没有直接对应的风险点。,[],190,长安区,西部大道,"路口:毕原一路西段与上林苑五路,方向:东,距离:263.137m",108.810628,34.170567
False,,,0.0,CSV中的积水点位于毕原一路西段与上林苑六路的交叉口，但Excel标准风险点列表中没有直接匹配的街道名称和路口描述。虽然毕原一路与上林苑六路在地理位置上可能相邻，但缺乏精确的路口匹配信息，因此无法确定匹配。,"['毕原一路西段', '上林苑六路']",191,长安区,毕原一路西段,"路口:毕原一路西段与上林苑六路,方向:南,距离:134.955m",108.803136,34.169497
True,26.0,XA66,1.0,CSV中的主要街道'西部大道'与Excel风险点列表中的'西部大道'完全匹配，且CSV中的行政区'长安区'与Excel风险点列表中的'长安区'一致，因此可以确定匹配。,"['街道名称', '行政区']",192,长安区,西部大道,"路口:西部大道与上林苑四路,方向:东北,距离:230.614m",108.815801,34.174849
True,26.0,XA66,1.0,CSV中的主要街道'西部大道'与Excel风险点列表中的'西部大道'完全匹配，且行政区'长安区'也一致，因此可以确定匹配。,"['主要街道', '行政区']",193,长安区,西部大道,"路口:定昆池三路与上林苑二路,方向:南,距离:184.14m",108.822758,34.175562
True,8.0,XA240,1.0,CSV中的积水点位于丈八八路与西部大道交叉口，距离西部大道409.922米，而Excel标准风险点列表中的编号为XA240的风险点描述为'丈八八路与西部大道十字以北'，虽然CSV中的积水点不在十字交叉口，但考虑到距离和街道名称的匹配，可以认为这是一个高置信度的匹配。,"['丈八八路', '西部大道', '交叉口', '距离']",195,长安区,西部大道,"路口:丈八八路与西部大道,方向:东北,距离:409.922m",108.834353,34.176989
False,,,0.0,CSV中的积水点位于定昆池二路与上林苑三路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然定昆池二路在Excel中未直接出现，但考虑到匹配策略中的精确匹配优先，没有找到完全匹配的记录。,"['定昆池二路', '上林苑三路', '交叉口']",196,长安区,定昆池二路,"路口:定昆池二路与上林苑三路,方向:西,距离:194.121m",108.815979,34.178416
True,8.0,XA240,1.0,CSV中的积水点位于丈八八路与西部大道交叉口，且距离为243.589m，这与Excel中编号为XA240的风险点描述‘丈八八路与西部大道十字以北’完全匹配。,"['丈八八路', '西部大道', '交叉口', '距离243.589m', '西部大道十字以北']",194,长安区,西部大道,"路口:丈八八路与西部大道,方向:西北,距离:243.589m",108.83025,34.175027
False,,,0.0,CSV中的积水点位于郭杜街道西长安街，路口为居安路与西长安街交汇处，但Excel标准风险点列表中没有直接匹配的街道名称、路口信息或包含的关键词。虽然Excel中有一个风险点位于西部大道，但该点与CSV中的积水点地理位置不匹配。,[],197,长安区,西长安街,"路口:居安路与西长安街,方向:东,距离:151.33m",108.881268,34.159329
False,,,0.0,CSV中的积水点位于文苑北路与樱花二路交叉口，但Excel标准风险点列表中没有直接匹配的交叉口描述。虽然CSV中的文苑北路与Excel风险点中的某些道路名称有部分重叠，但没有完全匹配的记录。,"['文苑北路', '樱花二路']",198,长安区,文苑北路,"路口:樱花二路与文苑北路,方向:东南,距离:181.661m",108.87663,34.165573
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的积水点位于郭杜东街，而Excel风险点列表中的街道名称与CSV中的街道名称不完全一致，且CSV中的路口详情与Excel风险点列表中的交叉口描述也没有直接匹配。此外，CSV中的积水点没有包含任何特殊关键词如'隧道'、'下穿'、'立交'、'桥'等，这些关键词在Excel风险点列表中的一些记录中是存在的。,[],199,长安区,郭杜东街,"路口:高阳四路与郭杜东街,方向:南,距离:274.755m",108.865927,34.158972
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'学府大街'，与Excel风险点列表中的'长安区学府大街'存在部分匹配，但路口详情'锦湖街与子午大道辅路'与Excel风险点列表中的任何交叉口描述都不匹配。此外，CSV中的地理位置信息'长安区郭杜街道'与Excel风险点列表中的街道信息不完全对应。,"['学府大街', '长安区']",200,长安区,学府大街,"路口:锦湖街与子午大道辅路,方向:西北,距离:148.288m",108.895182,34.140956
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为丽园路，而Excel风险点列表中没有直接提及丽园路。虽然CSV中的路口详情提到了艺术大街与怡园路的交叉口，但Excel风险点列表中没有提及这两个道路的交叉口。因此，无法确定CSV中的积水点与Excel中的哪个风险点匹配。,[],202,长安区,丽园路,"路口:艺术大街与怡园路,方向:西,距离:149.349m",108.870208,34.175027
False,,,0.0,CSV中的积水点位于韦曲街道广场北路，而Excel标准风险点列表中没有直接匹配的街道名称。虽然CSV中的路口详情提到了新华街与广场北路的交叉口，但Excel风险点列表中没有直接匹配的交叉口描述。因此，没有找到完全匹配的记录。,[],205,长安区,广场北路,"路口:新华街与广场北路,方向:东北,距离:223.039m",108.923366,34.163075
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的积水点位于韦曲北街，而Excel中的风险点主要集中在长安区的其他主要道路和交叉口，没有直接匹配的记录。,[],204,长安区,韦曲北街,"路口:杜市街与韦曲北街,方向:西,距离:69.0603m",108.916231,34.165573
True,6.0,XA229,1.0,CSV中的积水点位于新华街与太阳新街交叉口，CSV中的主要街道为新华街，这与Excel中编号为XA229的风险点描述‘新华街与太阳新城十字’完全匹配。,"['新华街', '太阳新城十字']",206,长安区,新华街,"路口:新华街与太阳新街,方向:东,距离:145.364m",108.927647,34.160578
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为'韦曲北街'，而Excel列表中的风险点均未提及此街道。路口详情中提到的'蒋张街与广场北路'交叉口在Excel列表中也没有对应的风险点。因此，无法确定积水点与风险点的匹配关系。,[],208,长安区,韦曲北街,"路口:蒋张街与广场北路,方向:东北,距离:178.557m",108.923901,34.168784
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的街道名称为'韦曲街道'，而Excel风险点列表中没有完全匹配的街道名称。CSV中的路口详情为'申店渡街与祥和巷，方向：西，距离：226.807m'，这与Excel风险点列表中的任何交叉口描述都不相符。此外，CSV中的行政区为'长安区'，但Excel风险点列表中的风险点并未具体到街道级别，因此无法通过行政区进行匹配。,[],207,长安区,南长安街,"路口:申店渡街与祥和巷,方向:西,距离:226.807m",108.933356,34.143453
False,,,0.0,CSV中的积水点位于韦曲北街，而Excel标准风险点列表中没有直接匹配的街道名称。虽然韦曲北街与韦曲街道相关，但Excel风险点列表中的街道名称与CSV中的不完全匹配。CSV中的路口详情为杜市街与清凉街交汇处，但Excel风险点列表中没有直接匹配的交叉口描述。因此，没有找到完全匹配的风险点。,[],209,长安区,韦曲北街,"路口:杜市街与清凉街,方向:西,距离:262.486m",108.915517,34.169854
False,,,0.0,CSV中的积水点位于韦曲街道南长安街，路口为樊川路与青年街交汇处，但Excel标准风险点列表中没有与这些信息完全匹配的记录。虽然韦曲街道和南长安街在长安区，但具体的路口和街道名称没有直接对应的风险点。,"['韦曲街道', '南长安街', '樊川路与青年街交汇']",216,长安区,南长安街,"路口:樊川路与青年街,方向:南,距离:129.582m",108.941249,34.153443
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都不完全匹配。CSV中的街道名称为'樊川路'，而Excel列表中没有直接匹配的街道名称。虽然CSV中提到了'神舟二路与少陵路'的路口，但Excel列表中没有包含这个具体的路口描述。因此，无法确定积水点与风险点的匹配关系。,[],217,长安区,樊川路,"路口:神舟二路与少陵路,方向:西北,距离:207.293m",108.952131,34.147556
True,2.0,XA64,0.9,CSV中的行政区为长安区，街道为韦曲街道，主要街道为欧亚大道，与Excel中的风险点编号XA64（长安区朱雀市场）的地理位置信息相符。虽然CSV中没有直接提到朱雀市场，但韦曲街道和欧亚大道的地理位置与朱雀市场相近，因此可以认为匹配度较高。,"['行政区：长安区', '街道：韦曲街道', '主要街道：欧亚大道']",210,长安区,欧亚大道,"路口:西部大道与靖宁路,方向:东北,距离:41.1836m",108.913734,34.174135
False,,,0.0,CSV中的积水点位于北长安街与韦曲北街交汇处，但Excel标准风险点列表中没有直接匹配的交叉口描述。虽然韦曲街道和长安区与某些风险点有部分重叠，但没有完全匹配的街道名称和路口详情。,"['北长安街', '韦曲街道', '韦曲北街']",219,长安区,北长安街,"路口:北长安街与韦曲北街,方向:东北,距离:302.611m",108.942141,34.164502
False,,,0.0,CSV中的积水点位于镐京大道与汉池一路交叉口，但该交叉口在Excel标准风险点列表中没有直接匹配项。虽然镐京大道在列表中多次出现，但交叉口名称和具体位置描述不匹配。,"['镐京大道', '汉池一路交叉口']",240,长安区,镐京大道,"路口:镐京大道与汉池一路,方向:北,距离:333.162m",108.793637,34.240538
True,17.0,XA181,0.9,CSV中的积水点位于神舟四路与航拓路交汇以南，这与Excel中编号为XA181的风险点描述'神州四路与航拓路交汇以南'完全匹配。虽然CSV中的积水点位于神舟四路，但考虑到交汇点的描述，可以认为这是一个精确匹配。,"['神舟四路', '航拓路', '交汇', '以南']",218,长安区,神舟四路,"路口:神舟四路与航拓路,方向:东北,距离:103.858m",108.970147,34.166643
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的积水点位于王寺南街，而Excel风险点列表中的街道名称与CSV中的街道名称不完全一致。此外，CSV中的路口详情为沣东南路与盛运路交叉口，而Excel风险点列表中没有包含这一具体路口信息。因此，无法确定匹配。,[],242,长安区,王寺南街,"路口:沣东南路与盛运路,方向:南,距离:241.416m",108.797561,34.25249
False,,,0.0,经过对比分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'广场南路'，而Excel风险点列表中没有完全匹配的街道名称。CSV中的路口详情为'惠民街与长兴街，方向：南，距离：121.464m'，但在Excel风险点列表中没有找到完全匹配的路口描述。尽管韦曲街道在Excel风险点列表中存在，但没有具体的路口或街道名称与之匹配。,[],288,长安区,广场南路,"路口:惠民街与长兴街,方向:南,距离:121.464m",108.924994,34.153203
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的街道名称为'征和四路'，而Excel列表中的风险点街道名称均为其他道路，如'丈八八路'、'靖宁路'等。CSV中的路口详情为'征和四路与太平路'，但Excel列表中的风险点交叉口描述均不包含此信息。因此，没有找到匹配的风险点。,[],249,长安区,征和四路,"路口:征和四路与太平路,方向:西南,距离:264.134m",108.791318,34.284599
True,41.0,XA188,0.9,CSV中的积水点位于长安区郭杜街道发展大道，与Excel中编号为XA188的风险点描述'发展大道与学士路十字'高度匹配，虽然CSV中没有提及学士路，但发展大道作为主要街道的匹配足以确认位置。,"['发展大道', '长安区', '郭杜街道']",292,长安区,发展大道,"路口:丈八六路与锦业四路,方向:东南,距离:147.312m",108.852392,34.174676
False,,,0.0,经过对比，CSV中的积水点位于韦曲街道西长安街润安路与西长安街交叉口，但Excel标准风险点列表中没有直接匹配的记录。虽然韦曲街道和西长安街在列表中存在，但具体的交叉口描述和位置信息不匹配。,"['韦曲街道', '西长安街', '润安路与西长安街交叉口']",289,长安区,西长安街,"路口:润安路与西长安街,方向:南,距离:80.6255m",108.918483,34.158019
False,,,0.0,经过对比，CSV中的积水点信息与Excel标准风险点列表中的任何记录都没有完全匹配。CSV中的主要街道为太阳新街，而Excel风险点列表中没有直接提及太阳新街。虽然韦曲街道在Excel风险点列表中出现过，但路口详情和地理位置信息不匹配。,[],291,长安区,太阳新街,"路口:韦曲北街与太阳新街,方向:南,距离:203.898m",108.926488,34.163192
