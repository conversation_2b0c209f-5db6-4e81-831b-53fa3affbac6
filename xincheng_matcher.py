import pandas as pd
import json
import requests
import time
from typing import List, Dict, Any
import config

class XinchengMatcher:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.base_url = config.BASE_URL
        self.model = config.MODEL_NAME
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_data(self):
        """加载CSV和Excel数据，筛选新城区"""
        # 加载新的CSV数据（积水点提取字段）
        self.csv_data = pd.read_csv('积水点_提取字段1.csv', encoding='gbk')
        self.xincheng_csv = self.csv_data[self.csv_data['district'] == '新城区'].copy()
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel(config.EXCEL_FILE)
        self.xincheng_excel = self.excel_data[self.excel_data['行政区'] == '新城区'].copy()
        
        print(f"CSV新城区数据: {len(self.xincheng_csv)} 条")
        print(f"Excel新城区数据: {len(self.xincheng_excel)} 条")
        
        # 显示CSV数据结构
        print(f"\nCSV列名: {self.csv_data.columns.tolist()}")
        print(f"\nCSV新城区样本数据:")
        for i, row in self.xincheng_csv.head(5).iterrows():
            print(f"  ID {row['id']}: {row['township']} {row['street_number_street']} | 路口: {row['roadinters_detail']}")
        
        # 显示Excel数据结构
        print(f"\nExcel新城区样本数据:")
        for i, row in self.xincheng_excel.head(10).iterrows():
            print(f"  {row['编号']}: {row['风险点']} ({row['风险等级']})")
        
        if config.ENABLE_PREVIEW:
            print(f"\n预览模式：只处理前 {config.PREVIEW_COUNT} 条CSV数据")
            self.xincheng_csv = self.xincheng_csv.head(config.PREVIEW_COUNT)
    
    def call_zhipu_api(self, prompt: str) -> str:
        """调用智谱AI API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=config.TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_detailed_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建详细的地址匹配提示词，使用CSV中的所有字段"""
        
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息（新城区）：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 行政区: {csv_record['district']}
- 街道: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 方向: {csv_record['street_number_direction']}
- 路口详情: {csv_record['roadinters_detail']}

Excel标准风险点列表（新城区）：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请逐一对比CSV积水点的各个字段与Excel风险点的匹配情况：

匹配分析要求：
1. **街道名称匹配**: 对比CSV的street_number_street字段与Excel风险点中的道路名称
2. **路口匹配**: 分析CSV的roadinters_detail字段与Excel风险点中的交叉口描述
3. **关键词匹配**: 注意"隧道"、"下穿"、"立交"、"桥"等关键词
4. **地理位置**: 考虑街道(township)信息辅助判断
5. **精确匹配优先**: 优先选择字段内容直接匹配的记录

匹配策略：
- 如果CSV的street_number_street与Excel风险点中的道路名完全匹配，高优先级
- 如果CSV的roadinters_detail中的路口信息与Excel风险点描述匹配，高优先级
- 注意Excel风险点中的特殊描述（如"隧道"、"下穿"等）
- 避免模糊匹配，确保匹配的准确性

请严格按照以下JSON格式返回结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程，说明哪些字段匹配",
    "matched_fields": {
        "street_match": "街道名称匹配情况",
        "intersection_match": "路口匹配情况", 
        "keyword_match": "关键词匹配情况"
    }
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        print(f"\n处理CSV记录 ID {csv_record['id']}")
        print(f"  地址: {csv_record['township']} {csv_record['street_number_street']}")
        print(f"  路口: {csv_record['roadinters_detail']}")
        
        # 创建匹配提示词
        prompt = self.create_detailed_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_zhipu_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            result['csv_id'] = csv_record['id']
            result['csv_street'] = csv_record['street_number_street']
            result['csv_intersection'] = csv_record['roadinters_detail']
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']}")
                    print(f"  置信度: {result.get('confidence', 0)}")
                    
                    matched_fields = result.get('matched_fields', {})
                    print(f"  街道匹配: {matched_fields.get('street_match', '无')}")
                    print(f"  路口匹配: {matched_fields.get('intersection_match', '无')}")
                    print(f"  关键词匹配: {matched_fields.get('keyword_match', '无')}")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配")
                print(f"  原因: {result.get('reason', '无原因')}")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"原始响应: {response[:200]}...")
            result = {
                'csv_id': csv_record['id'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record['roadinters_detail'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_fields': {}
            }
        
        return result
    
    def match_addresses(self):
        """批量匹配地址"""
        results = []
        excel_records = self.xincheng_excel.to_dict('records')
        
        for idx, csv_record in self.xincheng_csv.iterrows():
            result = self.match_single_record(csv_record, excel_records)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(config.DELAY_BETWEEN_CALLS)
        
        return results
    
    def save_results(self, results: List[Dict]):
        """保存匹配结果"""
        df_results = pd.DataFrame(results)
        
        # 保存详细结果
        df_results.to_csv('xincheng_matching_results.csv', index=False, encoding='utf-8-sig')
        
        # 创建匹配成功的汇总
        matched_results = [r for r in results if r.get('matched', False)]
        if matched_results:
            df_matched = pd.DataFrame(matched_results)
            df_matched.to_csv('xincheng_matched_summary.csv', index=False, encoding='utf-8-sig')
        
        # 统计匹配情况
        matched_count = len(matched_results)
        total_count = len(results)
        
        print(f"\n=== 新城区匹配完成 ===")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"详细结果已保存到: xincheng_matching_results.csv")
        if matched_results:
            print(f"匹配成功汇总已保存到: xincheng_matched_summary.csv")
        
        # 显示匹配成功的记录
        if matched_results:
            print(f"\n=== 匹配成功的记录 ===")
            for result in matched_results:
                print(f"CSV ID {result['csv_id']}: {result['excel_code']} (置信度: {result.get('confidence', 0)})")
                print(f"  CSV街道: {result['csv_street']}")
                print(f"  CSV路口: {result['csv_intersection']}")

def main():
    matcher = XinchengMatcher()
    matcher.load_data()
    
    print("\n开始新城区地址匹配...")
    results = matcher.match_addresses()
    matcher.save_results(results)

if __name__ == "__main__":
    main()
