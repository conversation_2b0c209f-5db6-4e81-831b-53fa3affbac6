# OpenRouter Gemini 地址匹配工具

## 功能说明

这个工具使用OpenRouter的Gemini 2.5 Flash模型来匹配CSV积水点数据和Excel风险点数据。

## 主要特性

1. **并发处理**: 支持多线程并发调用API，提高处理效率
2. **数据缓存**: 自动缓存已处理的结果，避免重复处理和数据丢失
3. **Excel集成**: 直接将匹配结果写入Excel文件，新增相关列
4. **全行政区支持**: 支持处理所有行政区的数据
5. **错误恢复**: 支持中断后从缓存恢复继续处理

## 文件说明

- `openrouter_gemini_matcher.py`: 主要的匹配程序
- `test_openrouter_gemini.py`: 测试程序，用于验证API连接和单条记录匹配
- `openrouter_config.py`: 配置文件，存储API密钥和其他设置
- `README_OpenRouter.md`: 本说明文档

## 使用步骤

### 1. 获取OpenRouter API密钥

1. 访问 https://openrouter.ai/
2. 注册账户并获取API密钥
3. 确保账户有足够的余额

### 2. 配置API密钥

有三种方式配置API密钥：

**方式1: 修改配置文件**
```python
# 编辑 openrouter_config.py
OPENROUTER_API_KEY = "your_actual_api_key_here"
```

**方式2: 设置环境变量**
```bash
export OPENROUTER_API_KEY="your_actual_api_key_here"
```

**方式3: 运行时输入**
程序运行时会提示输入API密钥

### 3. 测试API连接

首先运行测试程序确保API连接正常：

```bash
python test_openrouter_gemini.py
```

### 4. 运行完整匹配

确认测试通过后，运行完整的匹配程序：

```bash
python openrouter_gemini_matcher.py
```

## 输出文件

程序会生成以下文件：

1. **enhanced_ocr表格_gemini_YYYYMMDD_HHMMSS.xlsx**: 增强后的Excel文件，包含匹配信息
2. **gemini_matching_results.csv**: 详细的匹配结果CSV文件
3. **openrouter_matching_cache.pkl**: 缓存文件，用于断点续传

## 新增的Excel列

匹配成功后，Excel文件会新增以下列：

- **经度**: CSV记录的经度
- **纬度**: CSV记录的纬度  
- **csv_id**: CSV记录的ID
- **置信度**: 匹配置信度(0-1)
- **reason**: 详细的匹配分析过程
- **matched_elements**: 匹配的关键要素
- **csv_district**: CSV记录的行政区
- **csv_street**: CSV记录的街道
- **csv_intersection**: CSV记录的路口详情

## 配置参数

可以在 `openrouter_config.py` 中调整以下参数：

- `MAX_WORKERS`: 并发线程数（建议2-3，避免API限制）
- `DELAY_BETWEEN_REQUESTS`: 请求间隔时间（秒）
- `CACHE_SAVE_INTERVAL`: 缓存保存间隔（处理多少条记录后保存）
- `MAX_TOKENS`: API请求的最大token数
- `TEMPERATURE`: 模型温度参数

## 注意事项

1. **API费用**: Gemini 2.5 Flash是付费模型，请注意API调用费用
2. **速率限制**: 建议设置适当的并发数和延迟，避免触发API速率限制
3. **数据备份**: 处理前请备份原始数据文件
4. **网络连接**: 确保网络连接稳定，支持访问OpenRouter API

## 故障排除

### API连接失败
- 检查API密钥是否正确
- 检查网络连接
- 检查OpenRouter账户余额

### JSON解析错误
- 可能是API返回格式异常
- 检查API响应内容
- 尝试降低并发数

### 缓存问题
- 删除 `openrouter_matching_cache.pkl` 文件重新开始
- 检查磁盘空间是否充足

## 性能优化建议

1. 根据网络状况调整 `MAX_WORKERS` 参数
2. 设置合适的 `DELAY_BETWEEN_REQUESTS` 避免API限制
3. 定期清理缓存文件释放磁盘空间
4. 分批处理大量数据，避免长时间运行
