#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试逆地理编码功能
"""

import pandas as pd
from reverse_geocoding import AmapGeocoder

def test_single_point():
    """测试单个点的逆地理编码"""

    # 测试几个不同的点
    test_points = [
        (108.933768, 34.349163, "点1"),
        (108.928245, 34.348988, "点2"),
        (108.914918, 34.349339, "点3"),
        (108.943676, 34.369065, "点4"),
        (108.960685, 34.371827, "点5")
    ]

    print("=== 测试逆地理编码功能（道路交叉口版本）===")

    # 初始化地理编码器
    geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")

    for lng, lat, name in test_points:
        print(f"\n--- 测试 {name}: ({lng}, {lat}) ---")

        # 获取完整API响应（启用调试模式）
        result = geocoder.reverse_geocode(lng, lat, debug=True)

        if result:
            regeocode = result.get('regeocode', {})
            formatted_address = regeocode.get('formatted_address', '')
            roads = regeocode.get('roads', [])
            pois = regeocode.get('pois', [])

            print(f"📍 格式化地址: {formatted_address}")
            print(f"🛣️  道路数量: {len(roads)}")
            print(f"🏢 POI数量: {len(pois)}")

            if roads:
                print("道路详情:")
                for i, road in enumerate(roads[:3]):
                    print(f"  {i+1}. {road.get('name', '')} (距离:{road.get('distance', '')}m, 方向:{road.get('direction', '')})")

            if pois:
                print("POI详情:")
                for i, poi in enumerate(pois[:3]):
                    print(f"  {i+1}. {poi.get('name', '')} (距离:{poi.get('distance', '')}m)")
        else:
            print("❌ 未获取到数据")

        print("-" * 60)

    return True

def test_multiple_points():
    """测试多个点"""
    
    # 读取CSV文件的前5个点进行测试
    try:
        df = pd.read_csv("1h201.9-积水点.csv")
        test_df = df.head(5)  # 只测试前5个点
        
        print("\n=== 测试前5个点 ===")
        
        geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")
        
        for index, row in test_df.iterrows():
            lng = row['lon']
            lat = row['lat']
            point_id = row['id']
            
            print(f"\n--- 点 {point_id} ({lng}, {lat}) ---")
            result = geocoder.reverse_geocode(lng, lat, debug=False)

            if result:
                regeocode = result.get('regeocode', {})
                formatted_address = regeocode.get('formatted_address', '')
                print(f"📍 地址: {formatted_address}")
            else:
                print("❌ 获取失败")
            
    except Exception as e:
        print(f"测试多个点时出错: {e}")

if __name__ == "__main__":
    # 先测试单个点
    test_single_point()
    
    # 询问是否继续测试多个点
    user_input = input("\n是否测试多个点？(y/n): ").lower().strip()
    if user_input == 'y':
        test_multiple_points()
