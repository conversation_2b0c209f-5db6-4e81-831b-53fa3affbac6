#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从API响应中提取指定字段并格式化到CSV列中
提取字段：district、township、neighborhood、building、streetNumber、roads、roadinters
"""

import pandas as pd
import json
from typing import Dict, Any, List

def extract_fields_from_api_response(api_response) -> Dict[str, Any]:
    """从API响应中提取指定字段"""
    # 处理空值或NaN
    if pd.isna(api_response) or not api_response or api_response == '':
        return {
            'district': '',
            'township': '',
            'neighborhood_names': '',
            'neighborhood_types': '',
            'building_names': '',
            'building_types': '',
            'street_number_street': '',
            'street_number_number': '',
            'street_number_direction': '',
            'street_number_distance': '',
            'roads_detail': '',
            'roadinters_detail': ''
        }
    
    try:
        data = json.loads(api_response)
        regeocode = data.get('regeocode', {})
        addressComponent = regeocode.get('addressComponent', {})
        
        # 提取基本字段
        district = addressComponent.get('district', '')
        township = addressComponent.get('township', '')
        
        # 提取neighborhood信息
        neighborhood = addressComponent.get('neighborhood', {})
        neighborhood_names = '; '.join(neighborhood.get('name', []) if isinstance(neighborhood.get('name', []), list) else [])
        neighborhood_types = '; '.join(neighborhood.get('type', []) if isinstance(neighborhood.get('type', []), list) else [])
        
        # 提取building信息
        building = addressComponent.get('building', {})
        building_names = '; '.join(building.get('name', []) if isinstance(building.get('name', []), list) else [])
        building_types = '; '.join(building.get('type', []) if isinstance(building.get('type', []), list) else [])
        
        # 提取streetNumber信息
        streetNumber = addressComponent.get('streetNumber', {})
        street_number_street = streetNumber.get('street', '') if streetNumber else ''
        street_number_number = streetNumber.get('number', '') if streetNumber else ''
        street_number_direction = streetNumber.get('direction', '') if streetNumber else ''
        street_number_distance = streetNumber.get('distance', '') if streetNumber else ''
        
        # 提取roads信息
        roads = regeocode.get('roads', [])
        roads_detail = []
        for road in roads:
            road_info = f"名称:{road.get('name', '')},方向:{road.get('direction', '')},距离:{road.get('distance', '')}m,ID:{road.get('id', '')}"
            roads_detail.append(road_info)
        roads_detail_str = ' | '.join(roads_detail)
        
        # 提取roadinters信息
        roadinters = regeocode.get('roadinters', [])
        roadinters_detail = []
        for inter in roadinters:
            inter_info = f"路口:{inter.get('first_name', '')}与{inter.get('second_name', '')},方向:{inter.get('direction', '')},距离:{inter.get('distance', '')}m"
            roadinters_detail.append(inter_info)
        roadinters_detail_str = ' | '.join(roadinters_detail)
        
        return {
            'district': district,
            'township': township,
            'neighborhood_names': neighborhood_names,
            'neighborhood_types': neighborhood_types,
            'building_names': building_names,
            'building_types': building_types,
            'street_number_street': street_number_street,
            'street_number_number': street_number_number,
            'street_number_direction': street_number_direction,
            'street_number_distance': street_number_distance,
            'roads_detail': roads_detail_str,
            'roadinters_detail': roadinters_detail_str
        }
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return {
            'district': '',
            'township': '',
            'neighborhood_names': '',
            'neighborhood_types': '',
            'building_names': '',
            'building_types': '',
            'street_number_street': '',
            'street_number_number': '',
            'street_number_direction': '',
            'street_number_distance': '',
            'roads_detail': '',
            'roadinters_detail': ''
        }

def process_csv_file(input_file: str, output_file: str):
    """处理CSV文件，提取API响应中的指定字段"""
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"读取到 {len(df)} 条记录")
        
        if 'api_response' not in df.columns:
            print("错误: 文件中没有找到 'api_response' 列")
            return
        
        # 提取字段
        extracted_data = []
        error_count = 0
        for index, row in df.iterrows():
            api_response = row.get('api_response', '')
            try:
                fields = extract_fields_from_api_response(api_response)
                extracted_data.append(fields)
            except Exception as e:
                print(f"处理第 {index + 1} 条记录时出错: {e}")
                error_count += 1
                # 添加空字段
                fields = {
                    'district': '',
                    'township': '',
                    'neighborhood_names': '',
                    'neighborhood_types': '',
                    'building_names': '',
                    'building_types': '',
                    'street_number_street': '',
                    'street_number_number': '',
                    'street_number_direction': '',
                    'street_number_distance': '',
                    'roads_detail': '',
                    'roadinters_detail': ''
                }
                extracted_data.append(fields)

            if (index + 1) % 50 == 0:
                print(f"已处理 {index + 1} 条记录")

        if error_count > 0:
            print(f"处理过程中遇到 {error_count} 个错误")
        
        # 添加提取的字段到DataFrame
        for field_name in extracted_data[0].keys():
            df[field_name] = [data[field_name] for data in extracted_data]
        
        # 移除原始的api_response列（太长了）
        if 'api_response' in df.columns:
            df = df.drop('api_response', axis=1)
        
        # 保存结果
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n处理完成，结果保存到: {output_file}")
        
        # 显示统计信息
        print(f"\n字段统计:")
        print(f"- 有district信息的记录: {sum(1 for data in extracted_data if data['district'])}")
        print(f"- 有township信息的记录: {sum(1 for data in extracted_data if data['township'])}")
        print(f"- 有streetNumber信息的记录: {sum(1 for data in extracted_data if data['street_number_street'])}")
        print(f"- 有roads信息的记录: {sum(1 for data in extracted_data if data['roads_detail'])}")
        print(f"- 有roadinters信息的记录: {sum(1 for data in extracted_data if data['roadinters_detail'])}")
        
        # 显示前几条结果
        print(f"\n前3条结果预览:")
        display_cols = ['id', 'district', 'township', 'street_number_street', 'roads_detail']
        available_cols = [col for col in display_cols if col in df.columns]
        print(df[available_cols].head(3))
        
        # 显示列名
        print(f"\n输出文件包含的列:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")

def show_sample_data(input_file: str, num_samples: int = 3):
    """显示样本数据，用于检查API响应结构"""
    try:
        df = pd.read_csv(input_file)
        print(f"=== 样本数据分析 ===")
        
        for i in range(min(num_samples, len(df))):
            row = df.iloc[i]
            api_response = row.get('api_response', '')
            
            if api_response:
                try:
                    data = json.loads(api_response)
                    regeocode = data.get('regeocode', {})
                    addressComponent = regeocode.get('addressComponent', {})
                    
                    print(f"\n--- 样本 {i+1} (ID: {row.get('id', 'N/A')}) ---")
                    print(f"district: {addressComponent.get('district', '')}")
                    print(f"township: {addressComponent.get('township', '')}")
                    print(f"neighborhood: {addressComponent.get('neighborhood', {})}")
                    print(f"building: {addressComponent.get('building', {})}")
                    print(f"streetNumber: {addressComponent.get('streetNumber', {})}")
                    print(f"roads数量: {len(regeocode.get('roads', []))}")
                    print(f"roadinters数量: {len(regeocode.get('roadinters', []))}")
                    
                    # 显示第一个road的详细信息
                    roads = regeocode.get('roads', [])
                    if roads:
                        print(f"第一个road: {roads[0]}")
                    
                    # 显示第一个roadinter的详细信息
                    roadinters = regeocode.get('roadinters', [])
                    if roadinters:
                        print(f"第一个roadinter: {roadinters[0]}")
                        
                except json.JSONDecodeError as e:
                    print(f"样本 {i+1} JSON解析错误: {e}")
            else:
                print(f"样本 {i+1} 没有API响应数据")
                
    except Exception as e:
        print(f"分析样本数据时出错: {e}")

if __name__ == "__main__":
    input_file = "积水点_带地址.csv"
    output_file = "积水点_提取字段.csv"
    
    print("高德地图API字段提取工具")
    print("提取字段: district、township、neighborhood、building、streetNumber、roads、roadinters")
    print()
    
    # 先显示样本数据
    print("1. 分析样本数据...")
    show_sample_data(input_file, 2)
    
    print("\n" + "="*60)
    
    # 处理文件
    print("2. 开始提取字段...")
    process_csv_file(input_file, output_file)
    
    print(f"\n处理完成！")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
