import pandas as pd
import json
import requests
import time
import config
from typing import List, Dict, Tu<PERSON>
from datetime import datetime

class SingleDistrictMatcher:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.model = "glm-4-flash"
        self.base_url = config.BASE_URL
        self.timeout = 60
        self.temperature = 0.1
        self.max_tokens = 2000
        self.excel_batch_size = 3  # 每批处理的Excel记录数
        
        # 统计信息
        self.stats = {
            'processed_excel': 0,
            'total_matches': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'api_calls': 0,
            'api_errors': 0,
            'start_time': None
        }

    def create_matching_prompt(self, excel_records: List[Dict], csv_records: List[Dict], district: str) -> str:
        """创建匹配prompt"""
        
        prompt = f"""你是专业的地理位置匹配专家。请判断以下{district}的积水点风险评估数据与地图地址数据的匹配关系。

**严格匹配要求：**
1. 只有当两个地址明确指向同一个具体地点时才能匹配
2. 仅仅是同一条道路或附近区域不算匹配
3. 必须是相同的交叉口、立交桥、隧道等具体位置
4. 如果不确定，宁可不匹配也不要错误匹配
5. 置信度要求：high=100%确定，medium=80%确定，low=60%确定

**积水点风险数据：**
"""
        
        for i, record in enumerate(excel_records, 1):
            prompt += f"{i}. {record['point']}\n"
        
        prompt += f"\n**地图地址数据（共{len(csv_records)}条）：**\n"
        
        for i, record in enumerate(csv_records, 1):
            prompt += f"{i}. {record['road']} - {record['poi']}\n"
        
        prompt += f"""
**匹配示例：**
✅ 正确匹配："元朔大道立交" 与 "元朔大道与XX路交叉口" - 都是元朔大道的具体位置
❌ 错误匹配："元朔大道立交" 与 "明光路与XX路交叉口" - 完全不同的道路
❌ 错误匹配："下穿隧道" 与 "地面交叉口" - 不同的交通设施类型

**返回JSON格式：**
```json
{{
    "matches": [
        {{"excel_index": 1, "csv_index": 3, "confidence": "high", "reason": "具体详细的匹配理由，说明为什么确定是同一地点"}}
    ],
    "unmatched_excel": [2, 3],
    "unmatched_csv": [1, 2, 4, 5, ...]
}}
```

**再次强调：只匹配确定是同一地点的记录，不确定的不要匹配！**

只返回JSON，不要其他文字。"""

        return prompt

    def call_llm_api(self, prompt: str) -> Dict:
        """调用LLM API"""
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是专业的地理位置匹配专家。请严格按照要求进行匹配，只有100%确定是同一地点时才匹配。宁可漏匹配也不要错误匹配。严格按照JSON格式返回。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            self.stats['api_calls'] += 1
            
            if response.status_code == 200:
                result_data = response.json()
                content = result_data['choices'][0]['message']['content']
                
                # 解析JSON
                json_content = content.strip()
                if json_content.startswith('```json'):
                    json_content = json_content[7:]
                if json_content.endswith('```'):
                    json_content = json_content[:-3]
                json_content = json_content.strip()
                
                result = json.loads(json_content)
                return result
                
            else:
                print(f"❌ API调用失败: {response.status_code} - {response.text}")
                self.stats['api_errors'] += 1
                return {"error": f"API调用失败: {response.status_code}"}
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始内容: {content if 'content' in locals() else 'None'}")
            self.stats['api_errors'] += 1
            return {"error": f"JSON解析失败: {str(e)}"}
            
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            self.stats['api_errors'] += 1
            return {"error": str(e)}

    def process_single_district(self, excel_df: pd.DataFrame, csv_df: pd.DataFrame, district: str) -> List[Dict]:
        """处理单个行政区的数据"""
        
        print(f"\n=== 处理 {district} ===")
        self.stats['start_time'] = datetime.now()
        
        # 筛选该行政区的数据
        excel_district = excel_df[excel_df['行政区'] == district].reset_index()
        csv_district = csv_df[csv_df['dist'] == district].reset_index()
        
        print(f"📊 {district}: Excel {len(excel_district)} 条, CSV {len(csv_district)} 条")
        
        if len(excel_district) == 0 or len(csv_district) == 0:
            print(f"⚠️ {district} 数据不足，跳过")
            return []
        
        # 准备所有CSV记录
        csv_records = []
        for idx, row in csv_district.iterrows():
            csv_records.append({
                'original_index': row['index'],
                'batch_index': len(csv_records) + 1,
                'road': row['road_address'],
                'poi': row['poi_address'],
                'lon': row['lon'],
                'lat': row['lat'],
                'district': row['dist']
            })
        
        print(f"📍 CSV记录预览:")
        for i, record in enumerate(csv_records[:5]):
            print(f"  {record['batch_index']}: {record['road']} - {record['poi']}")
        if len(csv_records) > 5:
            print(f"  ... 还有{len(csv_records)-5}条记录")
        
        all_matches = []
        
        # 分批处理Excel数据
        excel_batches = (len(excel_district) + self.excel_batch_size - 1) // self.excel_batch_size
        
        for excel_batch_idx in range(0, len(excel_district), self.excel_batch_size):
            batch_excel = excel_district.iloc[excel_batch_idx:excel_batch_idx+self.excel_batch_size]
            
            print(f"\n🔄 Excel批次 {excel_batch_idx//self.excel_batch_size + 1}/{excel_batches}")
            
            # 准备Excel记录
            excel_records = []
            for idx, row in batch_excel.iterrows():
                excel_records.append({
                    'original_index': row['index'],
                    'batch_index': len(excel_records) + 1,
                    'point': row['风险点'],
                    'district': row['行政区']
                })
            
            print("📋 Excel记录:")
            for record in excel_records:
                print(f"  {record['batch_index']}: {record['point']}")
            
            print(f"📍 与所有{len(csv_records)}条CSV记录匹配")
            
            # 创建prompt并调用API
            prompt = self.create_matching_prompt(excel_records, csv_records, district)
            result = self.call_llm_api(prompt)
            
            if 'error' in result:
                print(f"❌ 批次处理失败: {result['error']}")
                continue
            
            # 处理匹配结果
            batch_matches = self.process_batch_result(result, excel_records, csv_records)
            all_matches.extend(batch_matches)
            
            # 更新统计
            self.stats['processed_excel'] += len(excel_records)
            
            # API调用间隔
            time.sleep(1)
        
        return all_matches

    def process_batch_result(self, result: Dict, excel_records: List[Dict], csv_records: List[Dict]) -> List[Dict]:
        """处理批次匹配结果"""
        
        batch_matches = []
        
        if 'matches' in result:
            for match in result['matches']:
                excel_idx = match['excel_index'] - 1
                csv_idx = match['csv_index'] - 1
                
                if excel_idx < len(excel_records) and csv_idx < len(csv_records):
                    excel_record = excel_records[excel_idx]
                    csv_record = csv_records[csv_idx]
                    
                    match_info = {
                        'excel_original_index': excel_record['original_index'],
                        'csv_original_index': csv_record['original_index'],
                        'excel_point': excel_record['point'],
                        'csv_road': csv_record['road'],
                        'csv_poi': csv_record['poi'],
                        'csv_lon': csv_record['lon'],
                        'csv_lat': csv_record['lat'],
                        'confidence': match['confidence'],
                        'reason': match['reason'],
                        'district': excel_record['district']
                    }
                    
                    batch_matches.append(match_info)
                    
                    # 更新统计
                    self.stats['total_matches'] += 1
                    if match['confidence'] == 'high':
                        self.stats['high_confidence'] += 1
                    elif match['confidence'] == 'medium':
                        self.stats['medium_confidence'] += 1
                    else:
                        self.stats['low_confidence'] += 1
                    
                    print(f"    ✅ 匹配: {excel_record['point'][:50]}...")
                    print(f"       -> {csv_record['road'][:50]}...")
                    print(f"       -> {csv_record['poi'][:50]}...")
                    print(f"       -> 坐标: ({csv_record['lon']}, {csv_record['lat']})")
                    print(f"       -> 置信度: {match['confidence']}")
                    print(f"       -> 理由: {match['reason']}")
                    print()
        
        return batch_matches

    def create_result_dataframe(self, excel_df: pd.DataFrame, matches: List[Dict], district: str) -> pd.DataFrame:
        """创建结果DataFrame"""
        
        print(f"\n=== 创建 {district} 结果文件 ===")
        
        # 只保留该行政区的数据
        district_excel = excel_df[excel_df['行政区'] == district].copy()
        
        # 添加新列
        district_excel['经度'] = None
        district_excel['纬度'] = None
        district_excel['道路地址'] = None
        district_excel['POI地址'] = None
        district_excel['匹配置信度'] = None
        district_excel['匹配理由'] = None
        district_excel['匹配状态'] = '未匹配'
        
        # 应用匹配结果
        for match in matches:
            # 在district_excel中找到对应的行
            mask = district_excel.index == match['excel_original_index']
            if mask.any():
                district_excel.loc[mask, '经度'] = match['csv_lon']
                district_excel.loc[mask, '纬度'] = match['csv_lat']
                district_excel.loc[mask, '道路地址'] = match['csv_road']
                district_excel.loc[mask, 'POI地址'] = match['csv_poi']
                district_excel.loc[mask, '匹配置信度'] = match['confidence']
                district_excel.loc[mask, '匹配理由'] = match['reason']
                district_excel.loc[mask, '匹配状态'] = '已匹配'
        
        return district_excel

    def print_final_stats(self, district: str):
        """打印最终统计信息"""
        
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print(f"\n=== {district} 匹配完成统计 ===")
        print(f"⏱️  总耗时: {duration}")
        print(f"🔄 已处理: {self.stats['processed_excel']} 条Excel记录")
        print(f"✅ 总匹配数: {self.stats['total_matches']}")
        print(f"🟢 高置信度: {self.stats['high_confidence']}")
        print(f"🟡 中置信度: {self.stats['medium_confidence']}")
        print(f"🔴 低置信度: {self.stats['low_confidence']}")
        print(f"📡 API调用: {self.stats['api_calls']}")
        print(f"❌ API错误: {self.stats['api_errors']}")
        
        # 计算匹配率
        if self.stats['processed_excel'] > 0:
            match_rate = (self.stats['total_matches'] / self.stats['processed_excel']) * 100
            print(f"📈 匹配率: {match_rate:.1f}%")

def main():
    """主函数"""
    
    # 检查配置
    if config.ZHIPU_API_KEY == "your-api-key-here":
        print("❌ 请先在config.py中设置API密钥")
        return
    
    print("=== 单个行政区地址匹配测试 ===")
    
    # 读取数据
    try:
        excel_df = pd.read_excel(config.EXCEL_FILE)
        csv_df = pd.read_csv(config.CSV_FILE, encoding='gbk')
        print(f"✅ 数据加载成功: Excel {len(excel_df)} 条, CSV {len(csv_df)} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 获取所有行政区
    excel_districts = set(excel_df['行政区'].dropna())
    csv_districts = set(csv_df['dist'].dropna())
    common_districts = excel_districts & csv_districts
    
    print(f"📍 可选行政区: {list(common_districts)}")
    
    # 显示每个行政区的数据量
    print(f"\n📊 各行政区数据统计:")
    for district in sorted(common_districts):
        excel_count = len(excel_df[excel_df['行政区'] == district])
        csv_count = len(csv_df[csv_df['dist'] == district])
        print(f"  {district}: Excel {excel_count} 条, CSV {csv_count} 条")
    
    # 选择要处理的行政区
    print(f"\n请选择要处理的行政区:")
    districts_list = sorted(list(common_districts))
    for i, district in enumerate(districts_list, 1):
        excel_count = len(excel_df[excel_df['行政区'] == district])
        csv_count = len(csv_df[csv_df['dist'] == district])
        print(f"  {i}. {district} (Excel {excel_count} 条, CSV {csv_count} 条)")
    
    try:
        choice = int(input(f"\n请输入选择 (1-{len(districts_list)}): "))
        if 1 <= choice <= len(districts_list):
            selected_district = districts_list[choice - 1]
        else:
            print("❌ 选择无效")
            return
    except ValueError:
        print("❌ 输入无效")
        return
    
    # 估算成本
    excel_count = len(excel_df[excel_df['行政区'] == selected_district])
    estimated_batches = (excel_count + 2) // 3  # 每批3条
    estimated_cost = estimated_batches * 0.25
    
    print(f"\n💰 预估处理 {selected_district}:")
    print(f"   Excel记录: {excel_count} 条")
    print(f"   预估API调用: {estimated_batches} 次")
    print(f"   预估成本: ¥{estimated_cost:.2f}")
    
    # 确认继续
    user_input = input(f"\n是否继续处理 {selected_district}？(y/n): ").lower().strip()
    if user_input != 'y':
        print("❌ 用户取消")
        return
    
    # 创建匹配器并处理
    matcher = SingleDistrictMatcher()
    matches = matcher.process_single_district(excel_df, csv_df, selected_district)
    
    # 创建结果DataFrame
    result_df = matcher.create_result_dataframe(excel_df, matches, selected_district)
    
    # 显示统计
    matcher.print_final_stats(selected_district)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存Excel结果
    excel_output = f'{selected_district}_匹配结果_{timestamp}.xlsx'
    result_df.to_excel(excel_output, index=False)
    print(f"📊 Excel结果已保存: {excel_output}")
    
    # 保存JSON详细结果
    json_output = f'{selected_district}_匹配详情_{timestamp}.json'

    # 处理datetime对象
    stats_copy = matcher.stats.copy()
    if 'start_time' in stats_copy and stats_copy['start_time']:
        stats_copy['start_time'] = str(stats_copy['start_time'])

    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump({
            'district': selected_district,
            'matches': matches,
            'stats': stats_copy,
            'timestamp': timestamp
        }, f, ensure_ascii=False, indent=2)
    print(f"📄 详细结果已保存: {json_output}")
    
    print(f"\n🎉 {selected_district} 匹配完成！")
    print(f"📁 结果文件: {excel_output}")
    print(f"📁 详情文件: {json_output}")

if __name__ == "__main__":
    main()
