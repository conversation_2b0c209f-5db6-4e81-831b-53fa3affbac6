import pandas as pd
import json
import requests
import time
from typing import List, Dict, Any
import config

class CompleteMatcher:
    def __init__(self, target_district=None):
        self.api_key = config.ZHIPU_API_KEY
        self.base_url = config.BASE_URL
        self.model = config.MODEL_NAME
        self.target_district = target_district
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载新的CSV数据（积水点提取字段）
        self.csv_data = pd.read_csv('积水点_提取字段1.csv', encoding='gbk')
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel(config.EXCEL_FILE)
        
        if self.target_district:
            # 筛选指定行政区
            self.filtered_csv = self.csv_data[self.csv_data['district'] == self.target_district].copy()
            self.filtered_excel = self.excel_data[self.excel_data['行政区'] == self.target_district].copy()
            print(f"{self.target_district} CSV数据: {len(self.filtered_csv)} 条")
            print(f"{self.target_district} Excel数据: {len(self.filtered_excel)} 条")
        else:
            # 处理所有数据
            self.filtered_csv = self.csv_data.copy()
            self.filtered_excel = self.excel_data.copy()
            print(f"全部 CSV数据: {len(self.filtered_csv)} 条")
            print(f"全部 Excel数据: {len(self.filtered_excel)} 条")
        
        # 显示各行政区的数据分布
        print(f"\nCSV数据行政区分布:")
        csv_districts = self.csv_data['district'].value_counts()
        for district, count in csv_districts.items():
            print(f"  {district}: {count} 条")
        
        print(f"\nExcel数据行政区分布:")
        excel_districts = self.excel_data['行政区'].value_counts()
        for district, count in excel_districts.items():
            print(f"  {district}: {count} 条")
    
    def call_zhipu_api(self, prompt: str) -> str:
        """调用智谱AI API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=config.TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建匹配提示词"""
        
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 行政区: {csv_record['district']}
- 街道: {csv_record['township']}
- 主要街道: {csv_record['street_number_street']}
- 方向: {csv_record['street_number_direction']}
- 路口详情: {csv_record['roadinters_detail']}

Excel标准风险点列表（{csv_record['district']}）：
"""
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请逐一对比CSV积水点的各个字段与Excel风险点的匹配情况：

匹配分析要求：
1. **街道名称匹配**: 对比CSV的street_number_street字段与Excel风险点中的道路名称
2. **路口匹配**: 分析CSV的roadinters_detail字段与Excel风险点中的交叉口描述
3. **关键词匹配**: 注意"隧道"、"下穿"、"立交"、"桥"等关键词
4. **地理位置**: 考虑街道(township)信息辅助判断
5. **精确匹配优先**: 优先选择字段内容直接匹配的记录

匹配策略：
- 如果CSV的street_number_street与Excel风险点中的道路名完全匹配，高优先级
- 如果CSV的roadinters_detail中的路口信息与Excel风险点描述匹配，高优先级
- 注意Excel风险点中的特殊描述（如"隧道"、"下穿"等）
- 避免模糊匹配，确保匹配的准确性

请严格按照以下JSON格式返回结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程",
    "matched_elements": ["匹配的关键要素列表"]
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        print(f"\n处理CSV记录 ID {csv_record['id']} ({csv_record['district']})")
        print(f"  地址: {csv_record['township']} {csv_record['street_number_street']}")
        
        # 创建匹配提示词
        prompt = self.create_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_zhipu_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            result['csv_id'] = csv_record['id']
            result['csv_district'] = csv_record['district']
            result['csv_street'] = csv_record['street_number_street']
            result['csv_intersection'] = csv_record['roadinters_detail']
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']}")
                    print(f"  置信度: {result.get('confidence', 0)}")
                    elements = result.get('matched_elements', [])
                    print(f"  匹配要素: {', '.join(elements)}")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            result = {
                'csv_id': csv_record['id'],
                'csv_district': csv_record['district'],
                'csv_street': csv_record['street_number_street'],
                'csv_intersection': csv_record['roadinters_detail'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_elements': []
            }
        
        return result
    
    def match_by_district(self):
        """按行政区分别匹配"""
        all_results = []
        
        # 获取所有行政区
        districts = self.filtered_csv['district'].unique()
        
        for district in districts:
            print(f"\n{'='*50}")
            print(f"开始处理 {district}")
            print(f"{'='*50}")
            
            # 筛选当前行政区的数据
            district_csv = self.filtered_csv[self.filtered_csv['district'] == district]
            district_excel = self.filtered_excel[self.filtered_excel['行政区'] == district]
            
            print(f"{district} CSV数据: {len(district_csv)} 条")
            print(f"{district} Excel数据: {len(district_excel)} 条")
            
            if len(district_excel) == 0:
                print(f"警告: {district} 没有Excel标准数据，跳过")
                continue
            
            excel_records = district_excel.to_dict('records')
            
            # 处理当前行政区的每条CSV记录
            for idx, csv_record in district_csv.iterrows():
                result = self.match_single_record(csv_record, excel_records)
                all_results.append(result)
                
                # 添加延迟避免API限制
                time.sleep(config.DELAY_BETWEEN_CALLS)
        
        return all_results
    
    def save_results(self, results: List[Dict]):
        """保存匹配结果"""
        df_results = pd.DataFrame(results)
        
        # 保存详细结果
        filename = f"complete_matching_results_{self.target_district or 'all'}.csv"
        df_results.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 创建匹配成功的汇总
        matched_results = [r for r in results if r.get('matched', False)]
        if matched_results:
            summary_filename = f"matched_summary_{self.target_district or 'all'}.csv"
            df_matched = pd.DataFrame(matched_results)
            df_matched.to_csv(summary_filename, index=False, encoding='utf-8-sig')
        
        # 统计匹配情况
        matched_count = len(matched_results)
        total_count = len(results)
        
        print(f"\n{'='*50}")
        print(f"匹配完成 - {self.target_district or '全部行政区'}")
        print(f"{'='*50}")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"详细结果已保存到: {filename}")
        if matched_results:
            print(f"匹配成功汇总已保存到: {summary_filename}")
        
        # 按行政区统计
        if not self.target_district:
            print(f"\n按行政区统计:")
            df_results_with_district = pd.DataFrame(results)
            district_stats = df_results_with_district.groupby('csv_district').agg({
                'matched': ['count', 'sum']
            }).round(2)
            district_stats.columns = ['总数', '匹配数']
            district_stats['匹配率%'] = (district_stats['匹配数'] / district_stats['总数'] * 100).round(1)
            print(district_stats)

def main():
    # 可以指定特定行政区，或者处理所有数据
    target_district = "新城区"  # 可以改为 None 处理所有行政区
    
    matcher = CompleteMatcher(target_district)
    matcher.load_data()
    
    print(f"\n开始地址匹配 - {target_district or '全部行政区'}...")
    results = matcher.match_by_district()
    matcher.save_results(results)

if __name__ == "__main__":
    main()
