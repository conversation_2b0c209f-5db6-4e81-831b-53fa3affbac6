#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 测试不同并发设置下的处理速度
"""

import pandas as pd
import time
from reverse_geocoding import AmapGeocoder, Config, process_single_point
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_performance(test_points=20, max_workers=5):
    """测试性能"""
    print(f"\n=== 性能测试 ===")
    print(f"测试点数: {test_points}")
    print(f"并发数: {max_workers}")
    
    # 读取测试数据
    try:
        df = pd.read_csv("1h201.9-积水点.csv")
        test_df = df.head(test_points)
        print(f"成功读取 {len(test_df)} 个测试点")
    except FileNotFoundError:
        print("错误: 找不到测试文件")
        return
    
    # 初始化地理编码器
    geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")
    
    # 准备任务
    tasks = []
    for index, row in test_df.iterrows():
        lng = row['lon']
        lat = row['lat']
        point_id = row['id']
        tasks.append((geocoder, lng, lat, point_id, index, len(test_df)))
    
    # 测试并发处理
    print(f"\n开始并发测试...")
    start_time = time.time()
    
    results = []
    success_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_index = {executor.submit(process_single_point, task): i for i, task in enumerate(tasks)}
        
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            try:
                result = future.result()
                results.append((index, result))
                if result:
                    success_count += 1
            except Exception as e:
                print(f"任务 {index + 1} 异常: {e}")
                results.append((index, None))
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 输出结果
    print(f"\n=== 测试结果 ===")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"平均每个点: {total_time/test_points:.2f} 秒")
    print(f"成功率: {success_count}/{test_points} ({success_count/test_points*100:.1f}%)")
    print(f"QPS: {test_points/total_time:.2f} 请求/秒")
    
    return {
        'total_time': total_time,
        'success_rate': success_count/test_points,
        'qps': test_points/total_time,
        'workers': max_workers
    }

def compare_performance():
    """比较不同并发数的性能"""
    print("=== 并发性能对比测试 ===")
    
    test_configs = [
        {'workers': 1, 'points': 10},
        {'workers': 3, 'points': 10},
        {'workers': 5, 'points': 10},
        {'workers': 8, 'points': 10},
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n测试配置: {config['workers']} 个线程, {config['points']} 个点")
        result = test_performance(config['points'], config['workers'])
        if result:
            results.append(result)
        time.sleep(2)  # 间隔2秒避免API限流
    
    # 输出对比结果
    print(f"\n=== 性能对比结果 ===")
    print(f"{'线程数':<6} {'总耗时(s)':<10} {'成功率':<8} {'QPS':<8}")
    print("-" * 35)
    
    for result in results:
        print(f"{result['workers']:<6} {result['total_time']:<10.2f} {result['success_rate']:<8.1%} {result['qps']:<8.2f}")
    
    # 找出最优配置
    if results:
        best_qps = max(results, key=lambda x: x['qps'])
        best_success = max(results, key=lambda x: x['success_rate'])
        
        print(f"\n推荐配置:")
        print(f"- 最高QPS: {best_qps['workers']} 线程 ({best_qps['qps']:.2f} QPS)")
        print(f"- 最高成功率: {best_success['workers']} 线程 ({best_success['success_rate']:.1%})")

def test_retry_mechanism():
    """测试重试机制"""
    print(f"\n=== 重试机制测试 ===")
    
    # 使用一个可能失败的坐标点进行测试
    geocoder = AmapGeocoder("0b65cd528f00db9f2cb533a1b2fee65f")
    
    test_coords = [
        (108.933768, 34.349163),  # 正常坐标
        (0, 0),                   # 可能失败的坐标
        (180, 90),                # 边界坐标
    ]
    
    for i, (lng, lat) in enumerate(test_coords):
        print(f"\n测试坐标 {i+1}: ({lng}, {lat})")
        start_time = time.time()
        
        result = geocoder.reverse_geocode(lng, lat, debug=True)
        
        end_time = time.time()
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        if result:
            print("✓ 成功获取数据")
        else:
            print("✗ 获取失败")

if __name__ == "__main__":
    print("地理编码性能测试工具")
    print("1. 基础性能测试")
    print("2. 并发性能对比")
    print("3. 重试机制测试")
    
    choice = input("\n请选择测试类型 (1-3): ").strip()
    
    if choice == "1":
        test_performance(20, 5)
    elif choice == "2":
        compare_performance()
    elif choice == "3":
        test_retry_mechanism()
    else:
        print("无效选择，运行默认测试")
        test_performance(10, 3)
