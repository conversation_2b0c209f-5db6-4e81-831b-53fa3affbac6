import pandas as pd
import json
import requests
import time
from typing import List, Dict, Any
import config

class ImprovedAddressMatcher:
    def __init__(self):
        self.api_key = config.ZHIPU_API_KEY
        self.base_url = config.BASE_URL
        self.model = config.MODEL_NAME
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_data(self):
        """加载CSV和Excel数据"""
        # 加载CSV数据（积水点带地址）
        self.csv_data = pd.read_csv(config.CSV_FILE, encoding='utf-8')
        self.weiyangqu_csv = self.csv_data[self.csv_data['district'] == '未央区'].copy()
        
        # 加载Excel数据（风险点标准数据）
        self.excel_data = pd.read_excel(config.EXCEL_FILE)
        self.weiyangqu_excel = self.excel_data[self.excel_data['行政区'] == '未央区'].copy()
        
        print(f"CSV未央区数据: {len(self.weiyangqu_csv)} 条")
        print(f"Excel未央区数据: {len(self.weiyangqu_excel)} 条")
        
        if config.ENABLE_PREVIEW:
            print(f"预览模式：只处理前 {config.PREVIEW_COUNT} 条CSV数据")
            self.weiyangqu_csv = self.weiyangqu_csv.head(config.PREVIEW_COUNT)
    
    def extract_comprehensive_address_info(self, csv_record: Dict) -> Dict:
        """提取CSV记录中的全面地址信息"""
        try:
            api_response = json.loads(csv_record['api_response'])
            regeocode = api_response['regeocode']
            
            # 基本地址信息
            info = {
                'formatted_address': regeocode.get('formatted_address', ''),
                'roads': [],
                'roadinters': [],
                'pois': [],
                'aois': [],
                'street_info': '',
                'township': ''
            }
            
            # 地址组件
            if 'addressComponent' in regeocode:
                addr_comp = regeocode['addressComponent']
                info['township'] = addr_comp.get('township', '')
                if 'streetNumber' in addr_comp:
                    street_num = addr_comp['streetNumber']
                    info['street_info'] = f"{street_num.get('street', '')} {street_num.get('number', '')}"
            
            # 道路信息（取前5条最近的）
            if 'roads' in regeocode:
                for road in regeocode['roads'][:5]:
                    info['roads'].append({
                        'name': road['name'],
                        'distance': float(road['distance']),
                        'direction': road['direction']
                    })
            
            # 路口信息（取前3个最近的）
            if 'roadinters' in regeocode:
                for inter in regeocode['roadinters'][:3]:
                    info['roadinters'].append({
                        'first_name': inter['first_name'],
                        'second_name': inter['second_name'],
                        'distance': float(inter['distance'])
                    })
            
            # POI信息（取前5个最近的）
            if 'pois' in regeocode:
                for poi in regeocode['pois'][:5]:
                    info['pois'].append({
                        'name': poi['name'],
                        'type': poi['type'],
                        'distance': float(poi['distance'])
                    })
            
            # AOI信息（取前3个最近的）
            if 'aois' in regeocode:
                for aoi in regeocode['aois'][:3]:
                    info['aois'].append({
                        'name': aoi['name'],
                        'type': aoi['type'],
                        'distance': float(aoi['distance'])
                    })
            
            return info
            
        except Exception as e:
            print(f"解析地址信息时出错: {e}")
            return {
                'formatted_address': csv_record.get('formatted_address', ''),
                'roads': [],
                'roadinters': [],
                'pois': [],
                'aois': [],
                'street_info': '',
                'township': ''
            }
    
    def call_zhipu_api(self, prompt: str) -> str:
        """调用智谱AI API"""
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=config.TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用错误: {e}")
            return ""
    
    def create_comprehensive_matching_prompt(self, csv_record: Dict, excel_records: List[Dict]) -> str:
        """创建综合地址匹配的提示词"""
        
        # 提取全面的地址信息
        addr_info = self.extract_comprehensive_address_info(csv_record)
        
        prompt = f"""你是一个专业的地址匹配专家。请帮我判断以下CSV数据中的积水点是否与Excel标准数据中的某个风险点匹配。

CSV积水点详细信息：
- ID: {csv_record['id']}
- 经纬度: ({csv_record['lon']}, {csv_record['lat']})
- 标准地址: {addr_info['formatted_address']}
- 街道信息: {addr_info['township']} {addr_info['street_info']}

道路信息（按距离排序）:"""
        
        for i, road in enumerate(addr_info['roads'], 1):
            prompt += f"\n  {i}. {road['name']} (距离: {road['distance']}米, 方向: {road['direction']})"
        
        if addr_info['roadinters']:
            prompt += f"\n\n路口信息:"
            for i, inter in enumerate(addr_info['roadinters'], 1):
                prompt += f"\n  {i}. {inter['first_name']} 与 {inter['second_name']} (距离: {inter['distance']}米)"
        
        if addr_info['pois']:
            prompt += f"\n\n周边POI信息:"
            for i, poi in enumerate(addr_info['pois'], 1):
                prompt += f"\n  {i}. {poi['name']} ({poi['type']}, 距离: {poi['distance']}米)"
        
        if addr_info['aois']:
            prompt += f"\n\n区域信息:"
            for i, aoi in enumerate(addr_info['aois'], 1):
                prompt += f"\n  {i}. {aoi['name']} (距离: {aoi['distance']}米)"
        
        prompt += f"\n\nExcel标准风险点列表:\n"
        
        for i, excel_record in enumerate(excel_records, 1):
            prompt += f"{i}. 编号: {excel_record['编号']}, 风险点: {excel_record['风险点']}, 风险等级: {excel_record['风险等级']}\n"
        
        prompt += """
请综合分析CSV积水点的所有地址信息，判断它是否与Excel列表中的某个风险点匹配。

匹配规则（按优先级排序）：
1. **道路名称匹配**: 优先匹配CSV中距离最近的道路与Excel风险点中的道路名称
2. **路口匹配**: 如果Excel风险点描述了交叉口，优先匹配CSV中的路口信息
3. **关键词匹配**: 注意"下穿"、"立交"、"桥下"、"隧道"等关键词
4. **地理位置相近性**: 考虑道路距离和方向信息
5. **POI和AOI辅助**: 利用周边兴趣点和区域信息辅助判断

匹配策略：
- 如果CSV中最近的道路（距离<50米）与Excel风险点道路名称匹配，优先考虑
- 如果CSV路口信息与Excel风险点描述的交叉口匹配，高度优先
- 避免将不同道路的积水点匹配到同一个风险点
- 如果没有明确匹配，宁可返回不匹配

请严格按照以下JSON格式返回结果：
{
    "matched": true/false,
    "excel_index": 匹配的Excel记录索引(1-based)，如果没有匹配则为null,
    "excel_code": 匹配的编号，如果没有匹配则为null,
    "confidence": 匹配置信度(0-1),
    "reason": "详细的匹配分析过程和原因",
    "matched_elements": ["匹配的关键要素，如道路名、路口等"]
}"""
        
        return prompt
    
    def match_single_record(self, csv_record: Dict, excel_records: List[Dict]) -> Dict:
        """匹配单条记录"""
        print(f"\n处理CSV记录 {csv_record['id']}: {csv_record['formatted_address']}")
        
        # 创建匹配提示词
        prompt = self.create_comprehensive_matching_prompt(csv_record, excel_records)
        
        # 调用API
        response = self.call_zhipu_api(prompt)
        
        try:
            # 清理响应文本，提取JSON部分
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # 解析JSON响应
            result = json.loads(response)
            result['csv_id'] = csv_record['id']
            result['csv_address'] = csv_record['formatted_address']
            
            if result.get('matched', False):
                excel_idx = result['excel_index'] - 1  # 转换为0-based索引
                if 0 <= excel_idx < len(excel_records):
                    excel_record = excel_records[excel_idx]
                    matched_elements = result.get('matched_elements', [])
                    print(f"✓ 匹配成功: {excel_record['编号']} - {excel_record['风险点']}")
                    print(f"  置信度: {result.get('confidence', 0)}")
                    print(f"  匹配要素: {', '.join(matched_elements)}")
                else:
                    print(f"✗ 索引错误: {result['excel_index']}")
                    result['matched'] = False
            else:
                print(f"✗ 未找到匹配: {result.get('reason', '无原因')}")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"原始响应: {response[:200]}...")
            result = {
                'csv_id': csv_record['id'],
                'csv_address': csv_record['formatted_address'],
                'matched': False,
                'excel_index': None,
                'excel_code': None,
                'confidence': 0,
                'reason': f'JSON解析错误: {str(e)}',
                'matched_elements': []
            }
        
        return result
    
    def match_addresses(self):
        """批量匹配地址"""
        results = []
        excel_records = self.weiyangqu_excel.to_dict('records')
        
        for idx, csv_record in self.weiyangqu_csv.iterrows():
            result = self.match_single_record(csv_record, excel_records)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(config.DELAY_BETWEEN_CALLS)
        
        return results
    
    def save_results(self, results: List[Dict]):
        """保存匹配结果"""
        df_results = pd.DataFrame(results)
        
        # 保存详细结果
        df_results.to_csv('improved_matching_results.csv', index=False, encoding='utf-8')
        
        # 创建匹配成功的汇总
        matched_results = [r for r in results if r.get('matched', False)]
        if matched_results:
            df_matched = pd.DataFrame(matched_results)
            df_matched.to_csv('improved_matched_summary.csv', index=False, encoding='utf-8')
        
        # 统计匹配情况
        matched_count = len(matched_results)
        total_count = len(results)
        
        print(f"\n=== 改进匹配完成 ===")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.1f}%")
        print(f"详细结果已保存到: improved_matching_results.csv")
        if matched_results:
            print(f"匹配成功汇总已保存到: improved_matched_summary.csv")
        
        # 显示匹配成功的记录
        if matched_results:
            print(f"\n=== 匹配成功的记录 ===")
            for result in matched_results:
                elements = ', '.join(result.get('matched_elements', []))
                print(f"CSV ID {result['csv_id']}: {result['excel_code']} (置信度: {result.get('confidence', 0)}, 匹配要素: {elements})")

def main():
    matcher = ImprovedAddressMatcher()
    matcher.load_data()
    
    print("\n开始改进的地址匹配...")
    results = matcher.match_addresses()
    matcher.save_results(results)

if __name__ == "__main__":
    main()
