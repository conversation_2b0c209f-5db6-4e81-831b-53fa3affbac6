#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积水点数据匹配脚本
将CSV文件中的积水点数据与Excel文件中的风险点数据进行匹配
"""

import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re
from typing import Tuple, Optional

def clean_text(text: str) -> str:
    """清理文本，去除特殊字符和多余空格"""
    if pd.isna(text):
        return ""
    # 去除括号内容和特殊字符
    text = re.sub(r'[（(].*?[）)]', '', str(text))
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    return text.strip()

def calculate_similarity(str1: str, str2: str) -> float:
    """计算两个字符串的相似度"""
    if not str1 or not str2:
        return 0.0
    
    # 清理文本
    clean_str1 = clean_text(str1)
    clean_str2 = clean_text(str2)
    
    if not clean_str1 or not clean_str2:
        return 0.0
    
    # 使用SequenceMatcher计算相似度
    similarity = SequenceMatcher(None, clean_str1, clean_str2).ratio()
    
    # 检查包含关系，提高相似度
    if clean_str1 in clean_str2 or clean_str2 in clean_str1:
        similarity = max(similarity, 0.8)
    
    return similarity

def find_best_match(csv_row: pd.Series, excel_df: pd.DataFrame, threshold: float = 0.3, cross_region: bool = False) -> Tuple[Optional[int], float]:
    """
    为CSV中的一行找到Excel中的最佳匹配

    Args:
        csv_row: CSV文件中的一行数据
        excel_df: Excel文件的DataFrame
        threshold: 相似度阈值
        cross_region: 是否允许跨区域匹配

    Returns:
        (匹配的Excel行索引, 相似度分数)
    """
    csv_region = csv_row['区域']
    csv_name = csv_row['名称']
    csv_location = csv_row['具体位置']

    # 首先按行政区筛选
    region_matches = excel_df[excel_df['行政区'] == csv_region]

    # 如果同区域没有匹配且允许跨区域匹配，则在全部数据中搜索
    if region_matches.empty and cross_region:
        region_matches = excel_df
        # 跨区域匹配时提高阈值
        threshold = max(threshold, 0.6)
    elif region_matches.empty:
        return None, 0.0

    best_match_idx = None
    best_score = 0.0

    for idx, excel_row in region_matches.iterrows():
        excel_risk_point = excel_row['风险点']

        # 计算名称相似度
        name_similarity = calculate_similarity(csv_name, excel_risk_point)

        # 计算位置相似度
        location_similarity = calculate_similarity(csv_location, excel_risk_point)

        # 综合相似度（名称权重更高）
        combined_score = name_similarity * 0.7 + location_similarity * 0.3

        if combined_score > best_score and combined_score >= threshold:
            best_score = combined_score
            best_match_idx = idx

    return best_match_idx, best_score

def match_data():
    """主要的数据匹配函数"""
    print("开始读取数据文件...")
    
    # 读取CSV文件
    try:
        csv_df = pd.read_csv('积水点基础信息及预警指标.csv', encoding='utf-8')
        print(f"成功读取CSV文件，共{len(csv_df)}行数据")
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return
    
    # 读取Excel文件
    try:
        excel_df = pd.read_excel('3h50mm.xlsx')
        print(f"成功读取Excel文件，共{len(excel_df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 准备结果DataFrame
    result_df = csv_df.copy()
    
    # 添加Excel文件中的列
    excel_columns = ['编号', '行政区', '风险点', '积水面积', '积水水量', '积水水深', '风险等级']
    for col in excel_columns:
        result_df[col] = np.nan
    
    # 添加匹配信息列
    result_df['匹配相似度'] = np.nan
    result_df['匹配状态'] = '未匹配'
    
    print("\n开始进行数据匹配...")
    
    matched_count = 0
    
    # 定义Excel中不存在的区域
    missing_regions = {'经开区', '航天基地', '浐灞国际港', '曲江新区', '高新区'}

    for idx, csv_row in csv_df.iterrows():
        print(f"处理第{idx+1}行: {csv_row['区域']} - {csv_row['名称']}")

        # 查找最佳匹配
        # 如果是缺失区域，允许跨区域匹配
        cross_region = csv_row['区域'] in missing_regions
        match_idx, similarity = find_best_match(csv_row, excel_df, cross_region=cross_region)

        if match_idx is not None:
            # 复制匹配的数据
            for col in excel_columns:
                result_df.loc[idx, col] = excel_df.loc[match_idx, col]

            result_df.loc[idx, '匹配相似度'] = round(similarity, 3)
            if cross_region:
                result_df.loc[idx, '匹配状态'] = '跨区域匹配'
            else:
                result_df.loc[idx, '匹配状态'] = '已匹配'

            matched_count += 1
            match_region = excel_df.loc[match_idx, '行政区']
            match_info = f"匹配到: {excel_df.loc[match_idx, '风险点']}"
            if cross_region:
                match_info += f" (跨区域: {match_region})"
            print(f"  -> 匹配成功，相似度: {similarity:.3f}, {match_info}")
        else:
            print(f"  -> 未找到匹配")
    
    print(f"\n匹配完成！")
    print(f"总计处理: {len(csv_df)} 条记录")
    print(f"成功匹配: {matched_count} 条记录")
    print(f"匹配率: {matched_count/len(csv_df)*100:.1f}%")
    
    # 保存结果
    output_filename = '积水点匹配结果.csv'
    result_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到: {output_filename}")
    
    # 显示匹配统计
    print("\n匹配统计:")
    print(result_df['匹配状态'].value_counts())
    
    # 显示未匹配的记录
    unmatched = result_df[result_df['匹配状态'] == '未匹配']
    if not unmatched.empty:
        print(f"\n未匹配的记录 ({len(unmatched)} 条):")
        for idx, row in unmatched.iterrows():
            print(f"  {row['区域']} - {row['名称']} - {row['具体位置']}")

if __name__ == "__main__":
    match_data()
