﻿matched,excel_index,excel_code,confidence,reason,matched_fields,csv_id,csv_street,csv_intersection
False,,,0.0,经过分析，CSV中的积水点位于太华路街道的八府庄园商业街与八府庄二坊交叉口，但Excel标准风险点列表中并没有直接匹配的街道名称或路口描述。虽然存在'八府庄隧道'这一风险点，但该点位于八府庄隧道而非八府庄园商业街与八府庄二坊交叉口，因此无法直接匹配。,"{'street_match': '不匹配', 'intersection_match': '不匹配', 'keyword_match': ""部分匹配（存在'八府庄隧道'关键词，但位置不匹配）""}",25,八府庄园商业街,"路口:八府庄园商业街与八府庄二坊,方向:北,距离:167.235m"
False,,,0.0,"经过分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。以下是详细的分析过程：
","{'street_match': 'CSV中的街道名称为自强路街道，Excel风险点列表中没有完全匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为西闸口与自强东路辅路，Excel风险点列表中没有完全匹配的路口描述。', 'keyword_match': ""CSV中的路口详情提到了'隧道'和'下穿'，但Excel风险点列表中没有包含这些关键词的匹配项。""}",42,二马路,"路口:西闸口与自强东路辅路,方向:西南,距离:68.3984m"
True,8.0,XA183,1.0,"CSV中的街道名称'解放门街道'与Excel风险点中的'西七路与北新路十字'存在直接匹配，且CSV中的路口详情'路口:北新街与西七路,方向:东,距离:64.951m'与Excel风险点描述中的'西七路与北新路十字'完全匹配，同时Excel风险点中的'西七路'与CSV中的'西七路'一致，因此匹配度高。","{'street_match': '街道名称匹配情况：解放门街道与西七路匹配', 'intersection_match': '路口匹配情况：北新街与西七路与西七路与北新路十字匹配', 'keyword_match': '关键词匹配情况：无特殊关键词匹配，但街道名称和路口信息直接匹配'}",43,西七路,"路口:北新街与西七路,方向:东,距离:64.951m"
False,,,0.0,经过分析，CSV中的积水点位于西五路，但Excel标准风险点列表中没有直接匹配的街道名称。虽然CSV中提到了西五路，但Excel中的风险点没有直接提及西五路，因此无法直接匹配。,"{'street_match': 'CSV中的街道名称为西五路，Excel风险点列表中没有完全匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为西五路与西五路辅路，Excel风险点列表中没有匹配的交叉口描述。', 'keyword_match': ""CSV中的关键词包括'西五路'，Excel风险点列表中没有直接匹配的关键词。""}",44,西五路,"路口:西五路与西五路辅路,方向:南,距离:52.4278m"
False,,,0.0,"经过分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。以下为详细分析过程：
","{'street_match': 'CSV中的主要街道为东五路，而Excel风险点列表中没有直接匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为环城东路北段辅路与东五路交叉口，但Excel风险点列表中没有直接匹配的交叉口描述。', 'keyword_match': ""CSV中的路口描述中包含'环城东路'和'东五路'，但没有包含Excel风险点列表中的关键词如'隧道'、'下穿'、'立交'、'桥'等。""}",51,东五路,"路口:环城东路北段辅路与东五路,方向:东,距离:59.5109m"
False,,,0.0,"经过分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。以下是详细分析过程：
","{'street_match': 'CSV中的主要街道为长缨西路，而Excel风险点列表中没有直接匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为环城东路北段辅路与长缨西路辅路交叉口，方向为西北，距离为1.65456m。Excel风险点列表中没有直接匹配的交叉口描述。', 'keyword_match': ""CSV中的路口描述中包含'辅路'，但Excel风险点列表中没有包含此关键词。""}",52,长缨西路,"路口:环城东路北段辅路与长缨西路辅路,方向:西北,距离:1.65456m"
False,,,0.0,经过分析，CSV中的积水点位于东八路与尚俭路/环城北路东段辅路的交叉口，但Excel标准风险点列表中没有直接匹配的街道名称或交叉口描述。虽然存在“隧道”、“下穿”、“立交”、“桥”等关键词，但它们并不直接指向CSV中的积水点。,"{'street_match': '不匹配', 'intersection_match': '不匹配', 'keyword_match': '不匹配'}",53,东八路,"路口:尚俭路与环城北路东段辅路,方向:东北,距离:83.2418m"
False,,,0.0,经过分析，CSV中的积水点位于万寿中路与咸宁中路辅路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然万寿路和咸宁中路在列表中存在，但交叉口描述和具体位置不匹配。,"{'street_match': '万寿中路与咸宁中路匹配，但不是完全匹配', 'intersection_match': '路口信息不匹配', 'keyword_match': '没有匹配的关键词'}",65,万寿中路,"路口:万寿路辅路与咸宁中路辅路,方向:东北,距离:44.8514m"
True,6.0,XA154,1.0,"CSV中的路口详情与Excel风险点中的描述完全匹配，具体如下：
CSV路口详情：路口:咸宁中路与公园南路, 方向:东北, 距离:9.40707m
Excel风险点描述：咸宁中路与公园南路十字
匹配字段：
- 街道名称匹配情况：公园南路与公园南路匹配
- 路口匹配情况：咸宁中路与公园南路匹配
- 关键词匹配情况：无特殊关键词匹配，但路口名称完全匹配","{'street_match': '匹配', 'intersection_match': '匹配', 'keyword_match': '无特殊关键词匹配'}",66,公园南路,"路口:咸宁中路与公园南路,方向:东北,距离:9.40707m"
True,5.0,XA7,0.9,CSV中的积水点位于韩森路与韩森东路交叉口，且该交叉口位于幸福中路，这与Excel中编号为XA7的风险点'幸福路韩森隧道'的描述相匹配。虽然CSV中没有直接提到'隧道'，但考虑到交叉口名称和街道名称的匹配，可以推断出两者相关。,"{'street_match': '幸福中路与Excel中的幸福路匹配', 'intersection_match': '韩森路与韩森东路交叉口与Excel中的韩森隧道匹配', 'keyword_match': ""虽然没有直接匹配'隧道'关键词，但交叉口名称和街道名称的匹配提供了足够的证据""}",67,幸福中路,"路口:韩森路与韩森东路,方向:西北,距离:111.55m"
False,,,0.0,经过分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录没有直接匹配。主要原因是CSV中的积水点位于胡家庙街道长缨西路，而Excel中的风险点主要集中在隧道、立交等特殊地点，没有与长缨西路直接相关的风险点。尽管CSV中提到了华清西路与康复路的交叉口，但Excel中的风险点列表中并没有提及该交叉口。,"{'street_match': 'CSV中的街道名称与Excel风险点列表中的街道名称不匹配。', 'intersection_match': 'CSV中的路口信息与Excel风险点列表中的交叉口描述不匹配。', 'keyword_match': ""CSV中的信息没有包含Excel风险点列表中的关键词如'隧道'、'下穿'、'立交'、'桥'等。""}",80,长缨西路,"路口:华清西路与康复路,方向:南,距离:107.977m"
False,,,0.0,"经过分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。以下是详细的分析过程：
","{'street_match': 'CSV中的主要街道为幸福北路，而Excel风险点列表中没有直接匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为长缨路与幸福路辅路，方向为南，距离为379.402m，这与Excel风险点列表中的任何交叉口描述都不匹配。', 'keyword_match': ""CSV中的关键词包括'长缨路'和'幸福路辅路'，但在Excel风险点列表中没有包含这些关键词的匹配项。""}",81,幸福北路,"路口:长缨路与幸福路辅路,方向:南,距离:379.402m"
False,,,0.0,经过分析，CSV中的积水点位于华清东路与酒一路、十里铺南路的交叉口，但Excel标准风险点列表中没有直接匹配的道路名称或交叉口描述。虽然华清东路与Excel中的华清东路与公园北路交汇以东有部分重合，但交叉口信息不完全匹配，因此无法确定精确匹配。,"{'street_match': '华清东路与华清东路与公园北路交汇以东有部分重合，但街道名称不完全匹配。', 'intersection_match': '酒一路与十里铺南路交叉口未在Excel风险点列表中找到。', 'keyword_match': ""CSV中的地点没有包含Excel风险点列表中的关键词如'隧道'、'下穿'、'立交'、'桥'。""}",85,华清东路,"路口:酒一路与十里铺南路,方向:西,距离:239.211m"
False,,,0.0,"经过分析，CSV中的积水点与Excel标准风险点列表中的任何记录都没有完全匹配。以下是详细的分析过程：
","{'street_match': 'CSV中的主要街道为幸福北路，而Excel风险点列表中没有直接匹配的街道名称。', 'intersection_match': 'CSV中的路口详情为长缨路与幸福路辅路，方向为北，距离为204.102m。Excel风险点列表中没有直接匹配的交叉口描述。', 'keyword_match': ""CSV中的路口描述中包含'长缨路'和'幸福路'，但没有包含'隧道'、'下穿'、'立交'、'桥'等关键词，因此没有与Excel风险点列表中的特殊描述匹配。""}",87,幸福北路,"路口:长缨路与幸福路辅路,方向:北,距离:204.102m"
False,,,0.0,"经过分析，CSV中的积水点信息与Excel标准风险点列表中的任何记录没有直接匹配。具体分析如下：
- 街道名称匹配：CSV中的主要街道为含元路，而Excel风险点列表中的道路名称没有与之完全匹配的。
- 路口匹配：CSV中的路口详情为华清东路辅路与华清路立交，而Excel风险点列表中没有描述该具体路口。
- 关键词匹配：CSV中的路口描述包含'立交'关键词，但Excel风险点列表中没有直接匹配的立交描述。
- 地理位置：CSV中的行政区为新城区，与Excel风险点列表中的所有风险点都在新城区相符，但具体位置不匹配。
- 精确匹配优先：由于没有直接匹配的字段，因此无法确定精确匹配。","{'street_match': '不匹配', 'intersection_match': '不匹配', 'keyword_match': '不匹配'}",88,含元路,"路口:华清东路辅路与华清路立交,方向:西北,距离:287.38m"
