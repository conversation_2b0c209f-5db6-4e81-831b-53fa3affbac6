#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反向积水点数据匹配脚本
以Excel文件为基准，将CSV文件中的积水点数据匹配并添加到Excel中
"""

import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re
from typing import Tuple, Optional

def clean_text(text: str) -> str:
    """清理文本，去除特殊字符和多余空格"""
    if pd.isna(text):
        return ""
    # 去除括号内容和特殊字符
    text = re.sub(r'[（(].*?[）)]', '', str(text))
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    return text.strip()

def calculate_similarity(str1: str, str2: str) -> float:
    """计算两个字符串的相似度"""
    if not str1 or not str2:
        return 0.0
    
    # 清理文本
    clean_str1 = clean_text(str1)
    clean_str2 = clean_text(str2)
    
    if not clean_str1 or not clean_str2:
        return 0.0
    
    # 使用SequenceMatcher计算相似度
    similarity = SequenceMatcher(None, clean_str1, clean_str2).ratio()
    
    # 检查包含关系，提高相似度
    if clean_str1 in clean_str2 or clean_str2 in clean_str1:
        similarity = max(similarity, 0.8)
    
    return similarity

def find_best_csv_match(excel_row: pd.Series, csv_df: pd.DataFrame, threshold: float = 0.3, cross_region: bool = False) -> Tuple[Optional[int], float]:
    """
    为Excel中的一行找到CSV中的最佳匹配
    
    Args:
        excel_row: Excel文件中的一行数据
        csv_df: CSV文件的DataFrame
        threshold: 相似度阈值
        cross_region: 是否允许跨区域匹配
    
    Returns:
        (匹配的CSV行索引, 相似度分数)
    """
    excel_region = excel_row['行政区']
    excel_risk_point = excel_row['风险点']
    
    # 首先按行政区筛选
    region_matches = csv_df[csv_df['区域'] == excel_region]
    
    # 如果同区域没有匹配且允许跨区域匹配，则在全部数据中搜索
    if region_matches.empty and cross_region:
        region_matches = csv_df
        # 跨区域匹配时提高阈值
        threshold = max(threshold, 0.6)
    elif region_matches.empty:
        return None, 0.0
    
    best_match_idx = None
    best_score = 0.0
    
    for idx, csv_row in region_matches.iterrows():
        csv_name = csv_row['名称']
        csv_location = csv_row['具体位置']
        
        # 计算名称相似度
        name_similarity = calculate_similarity(excel_risk_point, csv_name)
        
        # 计算位置相似度
        location_similarity = calculate_similarity(excel_risk_point, csv_location)
        
        # 综合相似度（名称权重更高）
        combined_score = name_similarity * 0.7 + location_similarity * 0.3
        
        if combined_score > best_score and combined_score >= threshold:
            best_score = combined_score
            best_match_idx = idx
    
    return best_match_idx, best_score

def reverse_match_data():
    """主要的反向数据匹配函数"""
    print("开始读取数据文件...")
    
    # 读取Excel文件（作为基准）
    try:
        excel_df = pd.read_excel('3h50mm.xlsx')
        print(f"成功读取Excel文件，共{len(excel_df)}行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 读取CSV文件
    try:
        csv_df = pd.read_csv('积水点基础信息及预警指标.csv', encoding='utf-8')
        print(f"成功读取CSV文件，共{len(csv_df)}行数据")
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return
    
    # 准备结果DataFrame（以Excel为基准）
    result_df = excel_df.copy()
    
    # 添加CSV文件中的列
    csv_columns = ['CSV序号', 'CSV区域', 'CSV名称', 'CSV具体位置', 'CSV经度', 'CSV纬度', 'CSV备注']
    for col in csv_columns:
        result_df[col] = np.nan
    
    # 添加匹配信息列
    result_df['匹配相似度'] = np.nan
    result_df['匹配状态'] = '未匹配'
    
    print("\n开始进行反向数据匹配...")
    
    matched_count = 0
    
    # 定义CSV中不存在的区域（在Excel中存在但CSV中可能缺失的区域）
    excel_regions = set(excel_df['行政区'].unique())
    csv_regions = set(csv_df['区域'].unique())
    missing_regions = excel_regions - csv_regions
    
    print(f"Excel中的区域: {excel_regions}")
    print(f"CSV中的区域: {csv_regions}")
    if missing_regions:
        print(f"Excel中有但CSV中没有的区域: {missing_regions}")
    
    for idx, excel_row in excel_df.iterrows():
        if (idx + 1) % 50 == 0:  # 每50行显示一次进度
            print(f"处理进度: {idx+1}/{len(excel_df)}")
        
        # 查找最佳匹配
        # 如果是缺失区域，允许跨区域匹配
        cross_region = excel_row['行政区'] in missing_regions
        match_idx, similarity = find_best_csv_match(excel_row, csv_df, cross_region=cross_region)
        
        if match_idx is not None:
            # 复制匹配的数据
            csv_row = csv_df.loc[match_idx]
            result_df.loc[idx, 'CSV序号'] = csv_row['序号']
            result_df.loc[idx, 'CSV区域'] = csv_row['区域']
            result_df.loc[idx, 'CSV名称'] = csv_row['名称']
            result_df.loc[idx, 'CSV具体位置'] = csv_row['具体位置']
            result_df.loc[idx, 'CSV经度'] = csv_row['经度']
            result_df.loc[idx, 'CSV纬度'] = csv_row['纬度']
            result_df.loc[idx, 'CSV备注'] = csv_row['备注']
            
            result_df.loc[idx, '匹配相似度'] = round(similarity, 3)
            if cross_region:
                result_df.loc[idx, '匹配状态'] = '跨区域匹配'
            else:
                result_df.loc[idx, '匹配状态'] = '已匹配'
            
            matched_count += 1
    
    print(f"\n匹配完成！")
    print(f"总计处理: {len(excel_df)} 条Excel记录")
    print(f"成功匹配: {matched_count} 条记录")
    print(f"匹配率: {matched_count/len(excel_df)*100:.1f}%")
    
    # 保存结果
    output_filename = 'Excel基准匹配结果.csv'
    result_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到: {output_filename}")
    
    # 显示匹配统计
    print("\n匹配统计:")
    print(result_df['匹配状态'].value_counts())
    
    # 显示匹配成功的样例
    matched_records = result_df[result_df['匹配状态'].isin(['已匹配', '跨区域匹配'])]
    if not matched_records.empty:
        print(f"\n匹配成功的记录样例 (前5条):")
        for idx, row in matched_records.head(5).iterrows():
            print(f"  Excel: {row['行政区']} - {row['风险点']} -> CSV: {row['CSV区域']} - {row['CSV名称']} (相似度: {row['匹配相似度']})")
    
    # 显示高相似度匹配
    high_similarity = result_df[result_df['匹配相似度'] > 0.9]
    print(f"\n高相似度匹配 (>0.9): {len(high_similarity)} 条")
    
    return result_df

if __name__ == "__main__":
    reverse_match_data()
