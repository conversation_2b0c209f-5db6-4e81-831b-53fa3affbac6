import pandas as pd
import json
import requests
import os
from datetime import datetime
from openrouter_gemini_matcher import OpenRouterGeminiMatcher

def run_xincheng_test():
    """运行新城区测试"""
    
    # 获取API密钥
    api_key = None
    
    try:
        import openrouter_config
        if hasattr(openrouter_config, 'OPENROUTER_API_KEY') and openrouter_config.OPENROUTER_API_KEY != "your_openrouter_api_key_here":
            api_key = openrouter_config.OPENROUTER_API_KEY
    except ImportError:
        pass
    
    if not api_key:
        api_key = os.getenv('OPENROUTER_API_KEY')
    
    if not api_key:
        print("错误: 需要提供OpenRouter API密钥")
        return
    
    # 创建匹配器
    matcher = OpenRouterGeminiMatcher(api_key, max_workers=1)  # 单线程测试
    matcher.load_data()
    
    # 只处理新城区数据
    xincheng_csv = matcher.csv_data[matcher.csv_data['district'] == '新城区']
    xincheng_excel = matcher.excel_data[matcher.excel_data['行政区'] == '新城区']
    
    print(f"\n新城区测试:")
    print(f"CSV数据: {len(xincheng_csv)} 条")
    print(f"Excel数据: {len(xincheng_excel)} 条")
    
    if len(xincheng_excel) == 0:
        print("错误: 新城区没有Excel数据")
        return
    
    excel_records = xincheng_excel.to_dict('records')
    results = []
    
    # 处理每条CSV记录
    for idx, csv_record in xincheng_csv.iterrows():
        print(f"\n处理 CSV ID {csv_record['id']}")
        result = matcher.match_single_record(csv_record, excel_records)
        results.append(result)
        
        # 保存缓存
        if len(results) % 3 == 0:
            matcher.save_cache()
    
    # 保存结果
    df_results = pd.DataFrame(results)
    
    # 保存详细结果
    df_results.to_csv('xincheng_test_results.csv', index=False, encoding='utf-8-sig')
    
    # 统计匹配情况
    matched_count = len(df_results[df_results['matched'] == True])
    total_count = len(results)
    
    print(f"\n{'='*50}")
    print(f"新城区测试完成")
    print(f"{'='*50}")
    print(f"总记录数: {total_count}")
    print(f"匹配成功: {matched_count}")
    print(f"匹配率: {matched_count/total_count*100:.1f}%")
    print(f"详细结果已保存到: xincheng_test_results.csv")
    
    # 显示匹配成功的记录
    matched_results = df_results[df_results['matched'] == True]
    if len(matched_results) > 0:
        print(f"\n匹配成功的记录:")
        for _, result in matched_results.iterrows():
            print(f"  CSV ID {result['csv_id']} -> Excel {result['excel_code']} (置信度: {result['confidence']})")
    
    # 最终保存缓存
    matcher.save_cache()
    
    return results

if __name__ == "__main__":
    run_xincheng_test()
