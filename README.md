# WGS84经纬度反查中文地址工具

这个工具可以根据WGS84格式的经纬度坐标，使用高德地图API反查对应的中文地址信息，并保存完整的API响应数据。

## 功能特点

- 支持WGS84坐标系到GCJ-02坐标系的自动转换
- 使用高德地图API进行逆地理编码
- **保存完整的API原始响应数据**，不进行二次处理
- 提供解析脚本，可从原始数据中提取各种格式的地址信息
- **高性能并发处理**，支持多线程同时请求，大幅提升处理速度
- **智能重试机制**，自动重试失败的请求，提高成功率
- **可配置参数**，支持调整并发数、重试次数等参数
- 批量处理CSV文件中的经纬度数据

## 工具组成

1. **`reverse_geocoding.py`** - 主要脚本，获取并保存高德地图API的完整响应
2. **`parse_api_response.py`** - 解析脚本，从API响应中提取各种格式的地址信息
3. **`test_geocoding.py`** - 测试脚本，用于验证功能
4. **`performance_test.py`** - 性能测试脚本，测试并发性能和重试机制

## 使用前准备

### 1. 申请高德地图API Key

1. 访问 [高德开放平台](https://console.amap.com/)
2. 注册并登录账号
3. 创建应用，选择"Web服务"类型
4. 获取API Key

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 配置API Key

编辑 `reverse_geocoding.py` 文件，将第118行的 `YOUR_AMAP_API_KEY_HERE` 替换为您的实际API Key：

```python
API_KEY = "您的高德地图API_KEY"
```

### 2. 运行主脚本获取API数据

```bash
python reverse_geocoding.py
```

这会生成 `积水点_带地址.csv` 文件，包含完整的高德地图API响应数据。

### 3. 解析API响应数据

```bash
python parse_api_response.py
```

这会生成 `积水点_详细地址.csv` 文件，包含解析后的各种格式地址信息。

### 4. 性能测试（可选）

```bash
python performance_test.py
```

可以测试不同并发设置下的处理性能，帮助优化配置。

## 输入文件格式

CSV文件应包含以下列：
- `lon`: 经度 (WGS84坐标系)
- `lat`: 纬度 (WGS84坐标系)  
- `id`: 点位ID

## 输出文件格式

### 第一步输出 (`积水点_带地址.csv`)
- `api_response`: 高德地图API的完整JSON响应数据
- `formatted_address`: 格式化地址（便于快速查看）
- `province`, `city`, `district`: 基本行政区划信息

### 第二步输出 (`积水点_详细地址.csv`)
在第一步的基础上增加：
- `road_address`: 道路地址（交叉路口、特殊道路类型、方向等）
- `poi_address`: POI地址（结合道路和具体地标）
- `road_info`: 提取的道路信息
- `poi_info`: 提取的POI信息
- `roads_detail`: 详细的道路列表
- `pois_detail`: 详细的POI列表

## 性能优化配置

### 并发设置
在 `reverse_geocoding.py` 中的 `Config` 类可以调整以下参数：

```python
class Config:
    MAX_WORKERS = 5      # 并发线程数，建议3-8之间
    MAX_RETRIES = 3      # 最大重试次数
    RETRY_DELAY = 0.5    # 重试延迟（秒）
    RETRY_BACKOFF = 2.0  # 重试延迟倍数
    REQUEST_TIMEOUT = 10 # 请求超时时间（秒）
```

### 性能建议
- **并发数**: 建议设置为3-8，过高可能被API限流
- **重试机制**: 自动处理网络异常和临时失败
- **批量处理**: 支持大批量数据的高效处理

## 注意事项

1. **API配额限制**: 高德地图API有每日调用次数限制，请根据您的套餐合理使用
2. **并发限制**: 建议并发数不超过8，避免触发API频率限制
3. **坐标系转换**: 脚本会自动将WGS84坐标转换为高德地图使用的GCJ-02坐标系
4. **错误处理**: 内置重试机制，自动处理临时失败的请求

## 常见问题

### Q: API Key无效怎么办？
A: 请检查API Key是否正确，以及是否已开通"逆地理编码"服务。

### Q: 部分地址获取失败怎么办？
A: 可能是网络问题或坐标超出服务范围，可以重新运行脚本处理失败的点。

### Q: 如何提高处理速度？
A: 可以适当减少延时时间，但要注意不要超过API的频率限制。

## 技术说明

- **坐标转换算法**: 使用标准的WGS84到GCJ-02转换算法
- **API接口**: 使用高德地图逆地理编码API v3
- **地址精度**: 返回到街道级别的地址信息
