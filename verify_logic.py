import pandas as pd

def verify_matching_logic():
    """验证匹配逻辑的执行流程"""
    
    # 加载数据
    csv_data = pd.read_csv('积水点_提取字段1.csv', encoding='gbk')
    excel_data = pd.read_excel('ocr表格1h201.9mm.xlsx')
    
    # 筛选新城区数据
    xincheng_csv = csv_data[csv_data['district'] == '新城区']
    xincheng_excel = excel_data[excel_data['行政区'] == '新城区']
    
    print("=== 匹配逻辑验证 ===")
    print(f"新城区 CSV记录数: {len(xincheng_csv)}")
    print(f"新城区 Excel记录数: {len(xincheng_excel)}")
    
    print(f"\n=== 执行逻辑说明 ===")
    print("当前程序的执行逻辑是：")
    print("1. 按行政区分组处理")
    print("2. 对于每条CSV记录，与该行政区的所有Excel记录进行对比")
    print("3. 大模型分析并返回最佳匹配结果")
    
    print(f"\n=== 新城区具体执行流程 ===")
    excel_records = xincheng_excel.to_dict('records')
    
    for i, (idx, csv_record) in enumerate(xincheng_csv.iterrows(), 1):
        print(f"\n第{i}次API调用:")
        print(f"  CSV记录: ID {csv_record['id']} - {csv_record['township']} {csv_record['street_number_street']}")
        print(f"  对比目标: 新城区所有{len(excel_records)}条Excel记录")
        print(f"  Excel记录列表:")
        for j, excel_record in enumerate(excel_records, 1):
            print(f"    {j}. {excel_record['编号']}: {excel_record['风险点']}")
        print(f"  → 大模型分析后返回最佳匹配结果")
    
    print(f"\n=== 总结 ===")
    print(f"总API调用次数: {len(xincheng_csv)} 次")
    print(f"每次调用处理: 1条CSV记录 vs {len(excel_records)}条Excel记录")
    print(f"这种方式的优点:")
    print(f"  - 减少API调用次数（{len(xincheng_csv)}次 vs {len(xincheng_csv) * len(excel_records)}次）")
    print(f"  - 大模型可以综合分析选择最佳匹配")
    print(f"  - 避免重复匹配到同一个Excel记录")

if __name__ == "__main__":
    verify_matching_logic()
